import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../data/models/user.dart';
import '../data/services/api_service.dart';
import '../data/services/storage_service.dart';

// Auth State
class AuthState {
  final User? user;
  final bool isAuthenticated;
  final bool isLoading;
  final String? error;

  const AuthState({
    this.user,
    this.isAuthenticated = false,
    this.isLoading = false,
    this.error,
  });

  AuthState copyWith({
    User? user,
    bool? isAuthenticated,
    bool? isLoading,
    String? error,
  }) {
    return AuthState(
      user: user ?? this.user,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Auth Notifier
class AuthNotifier extends StateNotifier<AuthState> {
  final ApiService _apiService;

  AuthNotifier(this._apiService) : super(const AuthState()) {
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    state = state.copyWith(isLoading: true);

    try {
      final token = await StorageService.getToken();
      final user = await StorageService.getUser();

      if (token != null && user != null) {
        state = state.copyWith(
          user: user,
          isAuthenticated: true,
          isLoading: false,
        );
      } else {
        state = state.copyWith(isAuthenticated: false, isLoading: false);
      }
    } catch (e) {
      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<bool> login(String username, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final request = AuthRequest(username: username, password: password);
      final response = await _apiService.login(request);

      // Handle direct response format (working API structure)
      if (response.containsKey('accessToken')) {
        final accessToken = response['accessToken'] as String;
        final tokenType = response['tokenType'] as String? ?? 'Bearer';
        final officeCode = response['officeCode'] as String?;

        final user = User(
          username: username,
          userType:
              'USER', // Default, can be determined from token or separate call
          userId: null, // Can be extracted from JWT token if needed
          roleId: null, // Can be extracted from JWT token if needed
        );

        // Save to storage
        await StorageService.saveToken(accessToken);
        await StorageService.saveUser(user);

        // Save office code if available
        if (officeCode != null) {
          await StorageService.saveOfficeCode(officeCode);
        }

        state = state.copyWith(
          user: user,
          isAuthenticated: true,
          isLoading: false,
        );

        return true;
      } else {
        // Handle authentication failure
        final errorMessage =
            response['message'] as String? ??
            response['error'] as String? ??
            'Invalid credentials';
        state = state.copyWith(isLoading: false, error: errorMessage);
        return false;
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  Future<void> logout() async {
    state = state.copyWith(isLoading: true);

    try {
      // Clear all stored data
      await StorageService.clearAll();

      // Reset state to initial values
      state = const AuthState(
        isAuthenticated: false,
        isLoading: false,
        user: null,
        error: null,
      );

      // Optional: Make API call to invalidate token on server
      // This would require implementing a logout endpoint
      // await _apiService.logout();
    } catch (e) {
      // Even if storage clearing fails, we should still log out locally
      state = const AuthState(
        isAuthenticated: false,
        isLoading: false,
        user: null,
        error: null,
      );

      // Re-throw the error so UI can handle it
      throw Exception('Logout failed: ${e.toString()}');
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Check if user is still authenticated by verifying stored token
  Future<void> checkAuthStatus() async {
    try {
      final token = await StorageService.getToken();
      final user = await StorageService.getUser();

      if (token != null && user != null) {
        // Token exists, update state if not already authenticated
        if (!state.isAuthenticated) {
          state = state.copyWith(
            isAuthenticated: true,
            user: user,
            isLoading: false,
          );
        }
      } else {
        // No token or user, ensure logged out state
        if (state.isAuthenticated) {
          state = const AuthState(
            isAuthenticated: false,
            isLoading: false,
            user: null,
            error: null,
          );
        }
      }
    } catch (e) {
      // Error reading storage, assume logged out
      state = const AuthState(
        isAuthenticated: false,
        isLoading: false,
        user: null,
        error: null,
      );
    }
  }
}

// Auth Provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final apiService = ref.read(apiServiceProvider);
  return AuthNotifier(apiService);
});

// Convenience providers
final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isAuthenticated;
});

final currentUserProvider = Provider<User?>((ref) {
  return ref.watch(authProvider).user;
});

final authLoadingProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isLoading;
});

final authErrorProvider = Provider<String?>((ref) {
  return ref.watch(authProvider).error;
});
