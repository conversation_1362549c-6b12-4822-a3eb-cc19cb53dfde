// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CustomerImpl _$$CustomerImplFromJson(Map<String, dynamic> json) =>
    _$CustomerImpl(
      customerId: (json['customerId'] as num?)?.toInt(),
      name: json['name'] as String,
      mobileNo: json['mobileNo'] as String,
      residentNo: json['residentNo'] as String?,
      address: json['address'] as String?,
      city: json['city'] as String?,
      products: (json['products'] as List<dynamic>?)
          ?.map((e) => Product.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$CustomerImplToJson(_$CustomerImpl instance) =>
    <String, dynamic>{
      'customerId': instance.customerId,
      'name': instance.name,
      'mobileNo': instance.mobileNo,
      'residentNo': instance.residentNo,
      'address': instance.address,
      'city': instance.city,
      'products': instance.products,
    };

_$CustomerCreateRequestImpl _$$CustomerCreateRequestImplFromJson(
  Map<String, dynamic> json,
) => _$CustomerCreateRequestImpl(
  name: json['name'] as String,
  mobileNo: json['mobileNo'] as String,
  residentNo: json['residentNo'] as String?,
  address: json['address'] as String?,
  city: json['city'] as String?,
);

Map<String, dynamic> _$$CustomerCreateRequestImplToJson(
  _$CustomerCreateRequestImpl instance,
) => <String, dynamic>{
  'name': instance.name,
  'mobileNo': instance.mobileNo,
  'residentNo': instance.residentNo,
  'address': instance.address,
  'city': instance.city,
};

_$CustomerListItemImpl _$$CustomerListItemImplFromJson(
  Map<String, dynamic> json,
) => _$CustomerListItemImpl(
  customerId: (json['customerId'] as num?)?.toInt(),
  name: json['name'] as String,
  mobileNo: json['mobileNo'] as String,
  residentNo: json['residentNo'] as String?,
  address: json['address'] as String?,
  city: json['city'] as String?,
  productCount: (json['productCount'] as num?)?.toInt(),
  complainCount: (json['complainCount'] as num?)?.toInt(),
);

Map<String, dynamic> _$$CustomerListItemImplToJson(
  _$CustomerListItemImpl instance,
) => <String, dynamic>{
  'customerId': instance.customerId,
  'name': instance.name,
  'mobileNo': instance.mobileNo,
  'residentNo': instance.residentNo,
  'address': instance.address,
  'city': instance.city,
  'productCount': instance.productCount,
  'complainCount': instance.complainCount,
};

_$CustomerSearchResponseImpl _$$CustomerSearchResponseImplFromJson(
  Map<String, dynamic> json,
) => _$CustomerSearchResponseImpl(
  responseCode: (json['responseCode'] as num).toInt(),
  responseMessage: json['responseMessage'] as String,
  response: (json['response'] as List<dynamic>?)
      ?.map((e) => CustomerListItem.fromJson(e as Map<String, dynamic>))
      .toList(),
  success: json['success'] as bool?,
  totalElements: (json['totalElements'] as num?)?.toInt(),
  totalPages: (json['totalPages'] as num?)?.toInt(),
  currentPage: (json['currentPage'] as num?)?.toInt(),
);

Map<String, dynamic> _$$CustomerSearchResponseImplToJson(
  _$CustomerSearchResponseImpl instance,
) => <String, dynamic>{
  'responseCode': instance.responseCode,
  'responseMessage': instance.responseMessage,
  'response': instance.response,
  'success': instance.success,
  'totalElements': instance.totalElements,
  'totalPages': instance.totalPages,
  'currentPage': instance.currentPage,
};
