// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProductImpl _$$ProductImplFromJson(Map<String, dynamic> json) =>
    _$ProductImpl(
      productId: (json['productId'] as num?)?.toInt(),
      productName: json['productName'] as String,
      brand: json['brand'] as String,
      description: json['description'] as String?,
      modelNumber: json['modelNumber'] as String?,
      serialNumber: json['serialNumber'] as String?,
      dateOfPurchase: json['dateOfPurchase'] == null
          ? null
          : DateTime.parse(json['dateOfPurchase'] as String),
      customerId: (json['customerId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$ProductImplToJson(_$ProductImpl instance) =>
    <String, dynamic>{
      'productId': instance.productId,
      'productName': instance.productName,
      'brand': instance.brand,
      'description': instance.description,
      'modelNumber': instance.modelNumber,
      'serialNumber': instance.serialNumber,
      'dateOfPurchase': instance.dateOfPurchase?.toIso8601String(),
      'customerId': instance.customerId,
    };

_$ProductCreateRequestImpl _$$ProductCreateRequestImplFromJson(
  Map<String, dynamic> json,
) => _$ProductCreateRequestImpl(
  productName: json['productName'] as String,
  brand: json['brand'] as String,
  description: json['description'] as String?,
  modelNumber: json['modelNumber'] as String?,
  serialNumber: json['serialNumber'] as String?,
  dateOfPurchase: json['dateOfPurchase'] == null
      ? null
      : DateTime.parse(json['dateOfPurchase'] as String),
);

Map<String, dynamic> _$$ProductCreateRequestImplToJson(
  _$ProductCreateRequestImpl instance,
) => <String, dynamic>{
  'productName': instance.productName,
  'brand': instance.brand,
  'description': instance.description,
  'modelNumber': instance.modelNumber,
  'serialNumber': instance.serialNumber,
  'dateOfPurchase': instance.dateOfPurchase?.toIso8601String(),
};

_$ProductListItemImpl _$$ProductListItemImplFromJson(
  Map<String, dynamic> json,
) => _$ProductListItemImpl(
  productId: (json['productId'] as num?)?.toInt(),
  productName: json['productName'] as String,
  brand: json['brand'] as String,
  description: json['description'] as String?,
  modelNumber: json['modelNumber'] as String?,
  serialNumber: json['serialNumber'] as String?,
  dateOfPurchase: json['dateOfPurchase'] == null
      ? null
      : DateTime.parse(json['dateOfPurchase'] as String),
  customerId: (json['customerId'] as num?)?.toInt(),
  customerName: json['customerName'] as String?,
  complainCount: (json['complainCount'] as num?)?.toInt(),
);

Map<String, dynamic> _$$ProductListItemImplToJson(
  _$ProductListItemImpl instance,
) => <String, dynamic>{
  'productId': instance.productId,
  'productName': instance.productName,
  'brand': instance.brand,
  'description': instance.description,
  'modelNumber': instance.modelNumber,
  'serialNumber': instance.serialNumber,
  'dateOfPurchase': instance.dateOfPurchase?.toIso8601String(),
  'customerId': instance.customerId,
  'customerName': instance.customerName,
  'complainCount': instance.complainCount,
};

_$ProductSearchResponseImpl _$$ProductSearchResponseImplFromJson(
  Map<String, dynamic> json,
) => _$ProductSearchResponseImpl(
  responseCode: (json['responseCode'] as num).toInt(),
  responseMessage: json['responseMessage'] as String,
  response: (json['response'] as List<dynamic>?)
      ?.map((e) => ProductListItem.fromJson(e as Map<String, dynamic>))
      .toList(),
  success: json['success'] as bool?,
  totalElements: (json['totalElements'] as num?)?.toInt(),
  totalPages: (json['totalPages'] as num?)?.toInt(),
  currentPage: (json['currentPage'] as num?)?.toInt(),
);

Map<String, dynamic> _$$ProductSearchResponseImplToJson(
  _$ProductSearchResponseImpl instance,
) => <String, dynamic>{
  'responseCode': instance.responseCode,
  'responseMessage': instance.responseMessage,
  'response': instance.response,
  'success': instance.success,
  'totalElements': instance.totalElements,
  'totalPages': instance.totalPages,
  'currentPage': instance.currentPage,
};
