// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserImpl _$$UserImplFromJson(Map<String, dynamic> json) => _$UserImpl(
  userId: (json['userId'] as num?)?.toInt(),
  username: json['username'] as String,
  userType: json['userType'] as String?,
  roleId: (json['roleId'] as num?)?.toInt(),
);

Map<String, dynamic> _$$UserImplToJson(_$UserImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'username': instance.username,
      'userType': instance.userType,
      'roleId': instance.roleId,
    };

_$AuthRequestImpl _$$AuthRequestImplFromJson(Map<String, dynamic> json) =>
    _$AuthRequestImpl(
      username: json['username'] as String,
      password: json['password'] as String,
    );

Map<String, dynamic> _$$AuthRequestImplToJson(_$AuthRequestImpl instance) =>
    <String, dynamic>{
      'username': instance.username,
      'password': instance.password,
    };

_$AuthResponseImpl _$$AuthResponseImplFromJson(Map<String, dynamic> json) =>
    _$AuthResponseImpl(
      accessToken: json['accessToken'] as String,
      userType: json['userType'] as String,
      userId: (json['userId'] as num?)?.toInt(),
      roleId: (json['roleId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$AuthResponseImplToJson(_$AuthResponseImpl instance) =>
    <String, dynamic>{
      'accessToken': instance.accessToken,
      'userType': instance.userType,
      'userId': instance.userId,
      'roleId': instance.roleId,
    };

_$StandardCreateResponseImpl _$$StandardCreateResponseImplFromJson(
  Map<String, dynamic> json,
) => _$StandardCreateResponseImpl(
  responseCode: (json['responseCode'] as num).toInt(),
  responseMessage: json['responseMessage'] as String,
  success: json['success'] as bool?,
);

Map<String, dynamic> _$$StandardCreateResponseImplToJson(
  _$StandardCreateResponseImpl instance,
) => <String, dynamic>{
  'responseCode': instance.responseCode,
  'responseMessage': instance.responseMessage,
  'success': instance.success,
};
