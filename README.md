# Complaint Management Mobile App

A Flutter mobile application for managing customer complaints and service requests, built with Riverpod state management.

## Features

- **Authentication**: JWT-based login system
- **Complaint Management**: Create, view, search, and manage complaints
- **Customer Management**: View customer information and history
- **Employee Management**: View employee directory
- **Real-time Updates**: Automatic data refresh and error handling
- **Offline Support**: Local storage for authentication tokens

## Architecture

- **State Management**: Riverpod for reactive state management
- **API Integration**: Dio for HTTP requests with JWT authentication
- **Navigation**: GoRouter for declarative routing
- **Data Models**: Freezed for immutable data classes
- **Local Storage**: Secure storage for tokens, SharedPreferences for user data

## Setup Instructions

### 1. Install Dependencies
```bash
flutter pub get
```

### 2. Generate Code (Required for Freezed models)
```bash
# On Windows
generate.bat

# On macOS/Linux
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### 3. Configure API Base URL
Update the base URL in `lib/core/constants/app_constants.dart`:
```dart
static const String baseUrl = 'https://your-domain.com/palakdev';
```

### 4. Run the Application
```bash
flutter run
```

## Project Structure

```
lib/
├── core/
│   ├── constants/          # App constants and configuration
│   └── utils/             # Utility functions and validators
├── data/
│   ├── models/            # Data models with Freezed
│   └── services/          # API and storage services
├── providers/             # Riverpod providers for state management
├── presentation/
│   ├── screens/           # UI screens organized by feature
│   └── widgets/           # Reusable UI components
└── main.dart             # App entry point
```

## Key Dependencies

- `flutter_riverpod`: State management
- `dio`: HTTP client for API calls
- `go_router`: Navigation and routing
- `freezed`: Immutable data classes
- `flutter_secure_storage`: Secure token storage
- `shared_preferences`: Local data persistence

## API Integration

The app integrates with a Spring Boot backend API with the following endpoints:

### Authentication
- `POST /auth/login` - User authentication

### Complaints
- `POST /user/complain/search` - Search complaints
- `POST /user/complain/create` - Create new complaint
- `GET /user/complain/view/{id}` - Get complaint details
- `POST /user/complain/assign/{complainId}/{employeeId}` - Assign employee
- `POST /user/complain/visit/{complainId}` - Create visit
- `POST /user/complain/close/{complainId}` - Close complaint

### Customers
- `POST /user/cust/search` - Search customers
- `POST /user/cust/create` - Create customer
- `GET /user/cust/search/{id}` - Get customer details

### Employees
- `POST /user/emp/search` - Search employees
- `POST /user/emp/create` - Create employee

## Development Notes

1. **Code Generation**: Run `flutter packages pub run build_runner build` after modifying Freezed models
2. **API Configuration**: Update base URL and endpoints in constants
3. **Authentication**: JWT tokens are automatically handled by the API service
4. **Error Handling**: Global error handling with toast messages
5. **Loading States**: Loading indicators for all async operations

## Building for Production

### Android
```bash
flutter build apk --release
```

### iOS
```bash
flutter build ios --release
```

## Contributing

1. Follow the existing code structure and patterns
2. Use Riverpod for state management
3. Implement proper error handling
4. Add loading states for async operations
5. Follow Material Design guidelines

## License

This project is part of the Complaint Management System.
