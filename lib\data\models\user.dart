import 'package:freezed_annotation/freezed_annotation.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
class User with _$User {
  const factory User({
    int? userId,
    required String username,
    String? userType,
    int? roleId,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}

@freezed
class AuthRequest with _$AuthRequest {
  const factory AuthRequest({
    required String username,
    required String password,
  }) = _AuthRequest;

  factory AuthRequest.fromJson(Map<String, dynamic> json) =>
      _$AuthRequestFromJson(json);
}

@freezed
class AuthResponse with _$AuthResponse {
  const factory AuthResponse({
    required String accessToken,
    required String userType,
    int? userId,
    int? roleId,
  }) = _AuthResponse;

  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);
}

// Generic response class without JSON serialization
class GenericResponse<T> {
  final int responseCode;
  final String responseMessage;
  final T? response;
  final bool? success;

  const GenericResponse({
    required this.responseCode,
    required this.responseMessage,
    this.response,
    this.success,
  });

  factory GenericResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object?) fromJsonT,
  ) {
    return GenericResponse<T>(
      responseCode: json['responseCode'] as int,
      responseMessage: json['responseMessage'] as String,
      response: json['response'] != null ? fromJsonT(json['response']) : null,
      success: json['success'] as bool?,
    );
  }
}

@freezed
class StandardCreateResponse with _$StandardCreateResponse {
  const factory StandardCreateResponse({
    required int responseCode,
    required String responseMessage,
    bool? success,
  }) = _StandardCreateResponse;

  factory StandardCreateResponse.fromJson(Map<String, dynamic> json) =>
      _$StandardCreateResponseFromJson(json);
}
