import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'core/constants/app_constants.dart';
import 'presentation/screens/auth/login_screen.dart';
import 'presentation/screens/auth/splash_screen.dart';
import 'presentation/screens/complaints/complaint_list_screen.dart';
import 'presentation/screens/complaints/complaint_detail_screen.dart';
import 'presentation/screens/complaints/create_complaint_screen.dart';
import 'presentation/screens/customers/customer_list_screen.dart';
import 'presentation/screens/customers/customer_detail_screen.dart';
import 'presentation/screens/employees/employee_list_screen.dart';

void main() {
  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp.router(
      title: AppConstants.appName,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
        useMaterial3: true,
        appBarTheme: const AppBarTheme(centerTitle: true, elevation: 2),
        inputDecorationTheme: InputDecorationTheme(
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
      routerConfig: _router,
    );
  }
}

final GoRouter _router = GoRouter(
  initialLocation: '/splash',
  routes: [
    GoRoute(path: '/splash', builder: (context, state) => const SplashScreen()),
    GoRoute(path: '/login', builder: (context, state) => const LoginScreen()),
    GoRoute(
      path: '/complaints',
      builder: (context, state) => const ComplaintListScreen(),
      routes: [
        GoRoute(
          path: 'detail/:id',
          builder: (context, state) => ComplaintDetailScreen(
            complainId: int.parse(state.pathParameters['id']!),
          ),
        ),
        GoRoute(
          path: 'create',
          builder: (context, state) => const CreateComplaintScreen(),
        ),
      ],
    ),
    GoRoute(
      path: '/customers',
      builder: (context, state) => const CustomerListScreen(),
      routes: [
        GoRoute(
          path: 'detail/:id',
          builder: (context, state) => CustomerDetailScreen(
            customerId: int.parse(state.pathParameters['id']!),
          ),
        ),
      ],
    ),
    GoRoute(
      path: '/employees',
      builder: (context, state) => const EmployeeListScreen(),
    ),
  ],
  redirect: (context, state) {
    // This will be handled by the splash screen
    return null;
  },
);
