# Project Flow Guide for complain_mobile

This guide explains the main flow and structure of the `complain_mobile` Flutter application, based on the codebase as of this writing. It is intended for new developers to quickly understand how the app works.

---

## 1. App Entry Point

- The app starts from `lib/main.dart`.
- The `main()` function wraps the app in a `ProviderScope` (for Riverpod state management) and launches `MyApp`.
- `MyApp` sets up the global theme and configures navigation using `GoRouter`.

---

## 2. Navigation Structure (GoRouter)

- The app uses `GoRouter` for navigation.
- The initial route is `/splash`.
- Main routes:
  - `/splash` → `SplashScreen`
  - `/login` → `LoginScreen`
  - `/complaints` → `ComplaintListScreen`
    - `/complaints/detail/:id` → `ComplaintDetailScreen`
    - `/complaints/create` → `CreateComplaintScreen`
  - `/customers` → `CustomerListScreen`
    - `/customers/detail/:id` → `CustomerDetailScreen`
  - `/employees` → `EmployeeListScreen`

---

## 3. Authentication Flow

- **SplashScreen** (`/splash`):
  - Shown on app launch.
  - Waits 2 seconds, then checks if the user is authenticated (via Riverpod provider).
  - If authenticated, navigates to `/complaints`.
  - If not, navigates to `/login`.

- **LoginScreen** (`/login`):
  - User enters username and password.
  - On successful login, navigates to `/complaints`.
  - Shows error toasts on failure.

---

## 4. Main Complaint Flow

- **ComplaintListScreen** (`/complaints`):
  - Shows a list of complaints (fetched via provider).
  - Has search and filter options (by complaint number and status).
  - Floating action button to create a new complaint.
  - Tapping a complaint navigates to its detail page.

- **ComplaintDetailScreen** (`/complaints/detail/:id`):
  - Shows detailed information about a complaint, including status, customer, product, assignment, and visit history.
  - Popup menu for actions (assign, create visit, close complaint) — currently shows 'coming soon' toasts.

- **CreateComplaintScreen** (`/complaints/create`):
  - Multi-step form (customer info → product info → complaint details).
  - Validates input at each step.
  - On submit, creates a new complaint and navigates back to the list.

---

## 5. Customer and Employee Flows

- **CustomerListScreen** (`/customers`):
  - Placeholder for customer list (feature coming soon).
  - Has search and add buttons (not yet implemented).

- **CustomerDetailScreen** (`/customers/detail/:id`):
  - Placeholder for customer details (feature coming soon).

- **EmployeeListScreen** (`/employees`):
  - Placeholder for employee list (feature coming soon).

---

## 6. State Management

- Uses [Riverpod](https://riverpod.dev/) for state management.
- Providers are used for authentication, complaints, etc.

---

## 7. Theming and UI

- Uses Material 3 theming.
- Consistent use of AppBar, Drawer, FloatingActionButton, and custom widgets.
- Responsive layouts for forms and lists.

---

## 8. Error Handling

- Uses `Fluttertoast` to show error and success messages.
- Handles loading and empty states with custom widgets.

---

## 9. Extending the App

- To add new features (e.g., customer/employee management), implement the TODOs in the respective screens and connect them to providers and services.
- For new navigation routes, add them to the GoRouter configuration in `main.dart`.

---

## 10. Summary

- The app is structured for easy navigation and state management.
- Main flows are authentication and complaint management.
- Customer and employee features are planned but not yet implemented.
- The codebase is modular and ready for further extension.

---

For more details, see the code in the `lib/presentation/screens/` directory and the providers in `lib/providers/`.

---

## 11. Technical Flow: API Calls, State, and Navigation

This section explains how the app technically works behind the scenes, including API calls, response handling, state management, navigation, and plugin usage.

### A. API Calls (Networking)
- **Plugin Used:** `dio`
- All API requests (login, complaints, customers, etc.) are made using the `ApiService` class (see `lib/data/services/api_service.dart`).
- The base URL and endpoints are set in `AppConstants`.
- JWT tokens and office codes are automatically added to requests using Dio interceptors.
- Example: When logging in, the app calls the `/auth-service/auth/login` endpoint with the user's credentials.

### B. Response Handling and Storage
- **Plugins Used:** `dio`, `flutter_secure_storage`, `shared_preferences`
- After a successful login, the access token is saved securely using `flutter_secure_storage` (for tokens) and user info is saved using `shared_preferences` (for user data).
- For other API calls (e.g., fetching complaints), the response is parsed into Dart models and stored in Riverpod state providers.
- If an error occurs (e.g., network error, invalid credentials), the error is stored in the provider's state and shown to the user via a toast.

### C. State Management
- **Plugin Used:** `flutter_riverpod`
- All app state (authentication, complaints, etc.) is managed using Riverpod providers and notifiers.
- When an API call completes, the provider's state is updated, which automatically updates the UI.
- Example: When a new complaint is created, the complaint list provider is refreshed to show the new data.

### D. Navigation After Actions
- **Plugin Used:** `go_router`
- After successful actions (login, create complaint, etc.), the app navigates to the appropriate screen using GoRouter.
- Example: After login, the user is redirected to the complaints list. After creating a complaint, the user is taken back to the complaints list.

### E. Showing Feedback to Users
- **Plugin Used:** `fluttertoast`
- Success and error messages are shown using toast notifications.
- Example: If login fails, a red toast is shown with the error message.

### F. Example: Login Flow (Step-by-Step)
1. User enters username and password and taps 'Sign In'.
2. The login method in the AuthNotifier calls `ApiService.login()` (using Dio).
3. If successful, the token and user info are saved using `flutter_secure_storage` and `shared_preferences`.
4. The Riverpod auth provider updates its state to authenticated.
5. The UI listens to this state and navigates to `/complaints`.
6. If there is an error, it is shown using `fluttertoast`.

### G. Example: Complaint List Fetch
1. The complaint list provider calls `ApiService.searchComplaints()`.
2. The response is parsed and stored in the provider's state.
3. The UI (ComplaintListScreen) listens to this state and displays the list.
4. Errors are shown using `fluttertoast`.

### H. Example: Create Complaint
1. User fills out the multi-step form and submits.
2. The provider calls `ApiService.createComplaint()`.
3. On success, a toast is shown and the app navigates back to the complaints list.
4. The complaint list provider is refreshed to show the new complaint.

### I. Plugin Usage Summary Table
| Plugin                  | Purpose/Where Used                                 |
|-------------------------|----------------------------------------------------|
| dio                     | All API/network requests                           |
| flutter_riverpod        | State management for all app logic                 |
| go_router               | Navigation between screens                         |
| flutter_secure_storage  | Securely storing JWT tokens                        |
| shared_preferences      | Storing user info and app preferences              |
| fluttertoast            | Showing error/success messages to the user         |

---

This technical flow ensures the app is robust, responsive, and easy to maintain. For more details, see the respective service, provider, and screen files in the codebase.
