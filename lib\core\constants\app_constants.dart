class AppConstants {
  // API Configuration - Development Environment (Working)
  static const String baseUrl = 'http://46.37.122.247:9502/';
  //static const String baseUrl = 'https://grumble.co.in/api/';
  static const String authServicePath = 'auth-service/auth';
  static const String complainServicePath = 'complain-service';

  // Storage Keys (matching React.js pattern)
  static const String tokenKey = 'authToken_grumble';
  static const String userKey = 'username_grumble';
  static const String officeCodeKey = 'officeCode';

  // App Configuration
  static const String appName = 'Complaint Management';
  static const int apiTimeout = 30000; // 30 seconds

  // Pagination
  static const int defaultPageSize = 50; // Matching React.js default

  // Date Formats
  static const String dateFormat = 'dd/MM/yyyy';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';

  // Complaint Status (matching backend)
  static const String statusNew = 'NEW';
  static const String statusPending = 'PENDING';
  static const String statusAssigned = 'ASSIGNED';
  static const String statusResolved = 'RESOLVED';
  static const String statusClosed = 'CLOSED';
  static const String statusCancelled = 'CANCELLED';

  // User Roles
  static const String roleAdmin = 'ADMIN';
  static const String roleEmployee = 'EMPLOYEE';
  static const String roleUser = 'USER';

  // Environment
  static const String environment = 'dev';
}
