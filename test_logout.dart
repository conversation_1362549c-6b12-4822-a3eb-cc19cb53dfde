import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'lib/data/services/storage_service.dart';
import 'lib/providers/auth_provider.dart';
import 'lib/data/models/user.dart';

/// Test logout functionality
/// Run with: flutter test test_logout.dart
void main() {
  group('Logout Tests', () {
    setUp(() async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
    });

    test('should clear all storage on logout', () async {
      // Arrange - Set up some stored data
      await StorageService.saveToken('test_token');
      await StorageService.saveUser(const User(
        username: 'testuser',
        userType: 'USER',
      ));
      await StorageService.saveOfficeCode('test_office');

      // Verify data is stored
      expect(await StorageService.getToken(), 'test_token');
      expect(await StorageService.getUser(), isNotNull);
      expect(await StorageService.getOfficeCode(), 'test_office');

      // Act - Perform logout
      await StorageService.clearAll();

      // Assert - Verify all data is cleared
      expect(await StorageService.getToken(), isNull);
      expect(await StorageService.getUser(), isNull);
      expect(await StorageService.getOfficeCode(), isNull);
    });

    test('should reset auth state on logout', () async {
      // This test would require setting up Riverpod container
      // For now, we'll test the storage clearing which is the core functionality
      
      // Arrange
      await StorageService.saveToken('test_token');
      await StorageService.saveUser(const User(
        username: 'testuser',
        userType: 'USER',
      ));

      // Verify initial state
      expect(await StorageService.getToken(), isNotNull);
      expect(await StorageService.getUser(), isNotNull);

      // Act - Clear storage (simulating logout)
      await StorageService.clearAll();

      // Assert
      expect(await StorageService.getToken(), isNull);
      expect(await StorageService.getUser(), isNull);
    });

    test('should handle logout errors gracefully', () async {
      // Test that logout doesn't throw exceptions even if storage is already empty
      expect(() async => await StorageService.clearAll(), returnsNormally);
      
      // Test multiple logout calls
      await StorageService.clearAll();
      expect(() async => await StorageService.clearAll(), returnsNormally);
    });
  });
}
