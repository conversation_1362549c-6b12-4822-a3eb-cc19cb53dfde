import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../../../providers/complaint_provider.dart';

import '../../../core/constants/app_constants.dart';

import '../../widgets/complaint_card.dart';
import '../../widgets/app_drawer.dart';
import '../../widgets/loading_widget.dart';

class ComplaintListScreen extends ConsumerStatefulWidget {
  const ComplaintListScreen({super.key});

  @override
  ConsumerState<ComplaintListScreen> createState() =>
      _ComplaintListScreenState();
}

class _ComplaintListScreenState extends ConsumerState<ComplaintListScreen> {
  final _scrollController = ScrollController();
  final _searchController = TextEditingController();
  String? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(complaintListProvider.notifier).searchComplaints(refresh: true);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      ref.read(complaintListProvider.notifier).loadMore();
    }
  }

  void _search() {
    ref
        .read(complaintListProvider.notifier)
        .searchComplaints(
          complainNumber: _searchController.text.trim().isEmpty
              ? null
              : _searchController.text.trim(),
          status: _selectedStatus,
          refresh: true,
        );
  }

  void _clearSearch() {
    _searchController.clear();
    _selectedStatus = null;
    ref.read(complaintListProvider.notifier).searchComplaints(refresh: true);
    setState(() {});
  }

  Future<void> _refresh() async {
    await ref.read(complaintListProvider.notifier).refresh();
  }

  @override
  Widget build(BuildContext context) {
    final complaintState = ref.watch(complaintListProvider);

    // Listen to errors
    ref.listen<String?>(complaintListProvider.select((state) => state.error), (
      previous,
      next,
    ) {
      if (next != null) {
        Fluttertoast.showToast(
          msg: next,
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );
        ref.read(complaintListProvider.notifier).clearError();
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Complaints'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
          ),
          IconButton(icon: const Icon(Icons.refresh), onPressed: _refresh),
        ],
      ),
      drawer: const AppDrawer(),
      body: Column(
        children: [
          // Search Bar
          if (_searchController.text.isNotEmpty || _selectedStatus != null)
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.grey[100],
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'Filtered results',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  TextButton(
                    onPressed: _clearSearch,
                    child: const Text('Clear'),
                  ),
                ],
              ),
            ),

          // Complaints List
          Expanded(
            child: complaintState.isLoading && complaintState.complaints.isEmpty
                ? const LoadingWidget(message: 'Loading complaints...')
                : complaintState.complaints.isEmpty
                ? _buildEmptyState()
                : RefreshIndicator(
                    onRefresh: _refresh,
                    child: ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      itemCount:
                          complaintState.complaints.length +
                          (complaintState.isLoadingMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index >= complaintState.complaints.length) {
                          return const Padding(
                            padding: EdgeInsets.all(16),
                            child: Center(child: CircularProgressIndicator()),
                          );
                        }

                        final complaint = complaintState.complaints[index];
                        return ComplaintCard(
                          complaint: complaint,
                          onTap: () => context.go(
                            '/complaints/detail/${complaint.complainId}',
                          ),
                        );
                      },
                    ),
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go('/complaints/create'),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inbox_outlined, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No complaints found',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Create a new complaint to get started',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => context.go('/complaints/create'),
            icon: const Icon(Icons.add),
            label: const Text('Create Complaint'),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Complaints'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                labelText: 'Complaint Number',
                hintText: 'Enter complaint number',
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedStatus,
              decoration: const InputDecoration(labelText: 'Status'),
              items: const [
                DropdownMenuItem(value: null, child: Text('All Status')),
                DropdownMenuItem(
                  value: AppConstants.statusPending,
                  child: Text('Pending'),
                ),
                DropdownMenuItem(
                  value: AppConstants.statusAssigned,
                  child: Text('Assigned'),
                ),
                DropdownMenuItem(
                  value: AppConstants.statusResolved,
                  child: Text('Resolved'),
                ),
                DropdownMenuItem(
                  value: AppConstants.statusCancelled,
                  child: Text('Cancelled'),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedStatus = value;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _search();
            },
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }
}
