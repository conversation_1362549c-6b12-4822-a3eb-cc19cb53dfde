// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'customer.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

Customer _$CustomerFromJson(Map<String, dynamic> json) {
  return _Customer.fromJson(json);
}

/// @nodoc
mixin _$Customer {
  int? get customerId => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get mobileNo => throw _privateConstructorUsedError;
  String? get residentNo => throw _privateConstructorUsedError;
  String? get address => throw _privateConstructorUsedError;
  String? get city => throw _privateConstructorUsedError;
  List<Product>? get products => throw _privateConstructorUsedError;

  /// Serializes this Customer to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Customer
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomerCopyWith<Customer> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomerCopyWith<$Res> {
  factory $CustomerCopyWith(Customer value, $Res Function(Customer) then) =
      _$CustomerCopyWithImpl<$Res, Customer>;
  @useResult
  $Res call({
    int? customerId,
    String name,
    String mobileNo,
    String? residentNo,
    String? address,
    String? city,
    List<Product>? products,
  });
}

/// @nodoc
class _$CustomerCopyWithImpl<$Res, $Val extends Customer>
    implements $CustomerCopyWith<$Res> {
  _$CustomerCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Customer
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customerId = freezed,
    Object? name = null,
    Object? mobileNo = null,
    Object? residentNo = freezed,
    Object? address = freezed,
    Object? city = freezed,
    Object? products = freezed,
  }) {
    return _then(
      _value.copyWith(
            customerId: freezed == customerId
                ? _value.customerId
                : customerId // ignore: cast_nullable_to_non_nullable
                      as int?,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            mobileNo: null == mobileNo
                ? _value.mobileNo
                : mobileNo // ignore: cast_nullable_to_non_nullable
                      as String,
            residentNo: freezed == residentNo
                ? _value.residentNo
                : residentNo // ignore: cast_nullable_to_non_nullable
                      as String?,
            address: freezed == address
                ? _value.address
                : address // ignore: cast_nullable_to_non_nullable
                      as String?,
            city: freezed == city
                ? _value.city
                : city // ignore: cast_nullable_to_non_nullable
                      as String?,
            products: freezed == products
                ? _value.products
                : products // ignore: cast_nullable_to_non_nullable
                      as List<Product>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CustomerImplCopyWith<$Res>
    implements $CustomerCopyWith<$Res> {
  factory _$$CustomerImplCopyWith(
    _$CustomerImpl value,
    $Res Function(_$CustomerImpl) then,
  ) = __$$CustomerImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int? customerId,
    String name,
    String mobileNo,
    String? residentNo,
    String? address,
    String? city,
    List<Product>? products,
  });
}

/// @nodoc
class __$$CustomerImplCopyWithImpl<$Res>
    extends _$CustomerCopyWithImpl<$Res, _$CustomerImpl>
    implements _$$CustomerImplCopyWith<$Res> {
  __$$CustomerImplCopyWithImpl(
    _$CustomerImpl _value,
    $Res Function(_$CustomerImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Customer
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customerId = freezed,
    Object? name = null,
    Object? mobileNo = null,
    Object? residentNo = freezed,
    Object? address = freezed,
    Object? city = freezed,
    Object? products = freezed,
  }) {
    return _then(
      _$CustomerImpl(
        customerId: freezed == customerId
            ? _value.customerId
            : customerId // ignore: cast_nullable_to_non_nullable
                  as int?,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        mobileNo: null == mobileNo
            ? _value.mobileNo
            : mobileNo // ignore: cast_nullable_to_non_nullable
                  as String,
        residentNo: freezed == residentNo
            ? _value.residentNo
            : residentNo // ignore: cast_nullable_to_non_nullable
                  as String?,
        address: freezed == address
            ? _value.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String?,
        city: freezed == city
            ? _value.city
            : city // ignore: cast_nullable_to_non_nullable
                  as String?,
        products: freezed == products
            ? _value._products
            : products // ignore: cast_nullable_to_non_nullable
                  as List<Product>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomerImpl implements _Customer {
  const _$CustomerImpl({
    this.customerId,
    required this.name,
    required this.mobileNo,
    this.residentNo,
    this.address,
    this.city,
    final List<Product>? products,
  }) : _products = products;

  factory _$CustomerImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomerImplFromJson(json);

  @override
  final int? customerId;
  @override
  final String name;
  @override
  final String mobileNo;
  @override
  final String? residentNo;
  @override
  final String? address;
  @override
  final String? city;
  final List<Product>? _products;
  @override
  List<Product>? get products {
    final value = _products;
    if (value == null) return null;
    if (_products is EqualUnmodifiableListView) return _products;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'Customer(customerId: $customerId, name: $name, mobileNo: $mobileNo, residentNo: $residentNo, address: $address, city: $city, products: $products)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomerImpl &&
            (identical(other.customerId, customerId) ||
                other.customerId == customerId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.mobileNo, mobileNo) ||
                other.mobileNo == mobileNo) &&
            (identical(other.residentNo, residentNo) ||
                other.residentNo == residentNo) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.city, city) || other.city == city) &&
            const DeepCollectionEquality().equals(other._products, _products));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    customerId,
    name,
    mobileNo,
    residentNo,
    address,
    city,
    const DeepCollectionEquality().hash(_products),
  );

  /// Create a copy of Customer
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomerImplCopyWith<_$CustomerImpl> get copyWith =>
      __$$CustomerImplCopyWithImpl<_$CustomerImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomerImplToJson(this);
  }
}

abstract class _Customer implements Customer {
  const factory _Customer({
    final int? customerId,
    required final String name,
    required final String mobileNo,
    final String? residentNo,
    final String? address,
    final String? city,
    final List<Product>? products,
  }) = _$CustomerImpl;

  factory _Customer.fromJson(Map<String, dynamic> json) =
      _$CustomerImpl.fromJson;

  @override
  int? get customerId;
  @override
  String get name;
  @override
  String get mobileNo;
  @override
  String? get residentNo;
  @override
  String? get address;
  @override
  String? get city;
  @override
  List<Product>? get products;

  /// Create a copy of Customer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomerImplCopyWith<_$CustomerImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CustomerCreateRequest _$CustomerCreateRequestFromJson(
  Map<String, dynamic> json,
) {
  return _CustomerCreateRequest.fromJson(json);
}

/// @nodoc
mixin _$CustomerCreateRequest {
  String get name => throw _privateConstructorUsedError;
  String get mobileNo => throw _privateConstructorUsedError;
  String? get residentNo => throw _privateConstructorUsedError;
  String? get address => throw _privateConstructorUsedError;
  String? get city => throw _privateConstructorUsedError;

  /// Serializes this CustomerCreateRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomerCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomerCreateRequestCopyWith<CustomerCreateRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomerCreateRequestCopyWith<$Res> {
  factory $CustomerCreateRequestCopyWith(
    CustomerCreateRequest value,
    $Res Function(CustomerCreateRequest) then,
  ) = _$CustomerCreateRequestCopyWithImpl<$Res, CustomerCreateRequest>;
  @useResult
  $Res call({
    String name,
    String mobileNo,
    String? residentNo,
    String? address,
    String? city,
  });
}

/// @nodoc
class _$CustomerCreateRequestCopyWithImpl<
  $Res,
  $Val extends CustomerCreateRequest
>
    implements $CustomerCreateRequestCopyWith<$Res> {
  _$CustomerCreateRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomerCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? mobileNo = null,
    Object? residentNo = freezed,
    Object? address = freezed,
    Object? city = freezed,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            mobileNo: null == mobileNo
                ? _value.mobileNo
                : mobileNo // ignore: cast_nullable_to_non_nullable
                      as String,
            residentNo: freezed == residentNo
                ? _value.residentNo
                : residentNo // ignore: cast_nullable_to_non_nullable
                      as String?,
            address: freezed == address
                ? _value.address
                : address // ignore: cast_nullable_to_non_nullable
                      as String?,
            city: freezed == city
                ? _value.city
                : city // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CustomerCreateRequestImplCopyWith<$Res>
    implements $CustomerCreateRequestCopyWith<$Res> {
  factory _$$CustomerCreateRequestImplCopyWith(
    _$CustomerCreateRequestImpl value,
    $Res Function(_$CustomerCreateRequestImpl) then,
  ) = __$$CustomerCreateRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String mobileNo,
    String? residentNo,
    String? address,
    String? city,
  });
}

/// @nodoc
class __$$CustomerCreateRequestImplCopyWithImpl<$Res>
    extends
        _$CustomerCreateRequestCopyWithImpl<$Res, _$CustomerCreateRequestImpl>
    implements _$$CustomerCreateRequestImplCopyWith<$Res> {
  __$$CustomerCreateRequestImplCopyWithImpl(
    _$CustomerCreateRequestImpl _value,
    $Res Function(_$CustomerCreateRequestImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CustomerCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? mobileNo = null,
    Object? residentNo = freezed,
    Object? address = freezed,
    Object? city = freezed,
  }) {
    return _then(
      _$CustomerCreateRequestImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        mobileNo: null == mobileNo
            ? _value.mobileNo
            : mobileNo // ignore: cast_nullable_to_non_nullable
                  as String,
        residentNo: freezed == residentNo
            ? _value.residentNo
            : residentNo // ignore: cast_nullable_to_non_nullable
                  as String?,
        address: freezed == address
            ? _value.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String?,
        city: freezed == city
            ? _value.city
            : city // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomerCreateRequestImpl implements _CustomerCreateRequest {
  const _$CustomerCreateRequestImpl({
    required this.name,
    required this.mobileNo,
    this.residentNo,
    this.address,
    this.city,
  });

  factory _$CustomerCreateRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomerCreateRequestImplFromJson(json);

  @override
  final String name;
  @override
  final String mobileNo;
  @override
  final String? residentNo;
  @override
  final String? address;
  @override
  final String? city;

  @override
  String toString() {
    return 'CustomerCreateRequest(name: $name, mobileNo: $mobileNo, residentNo: $residentNo, address: $address, city: $city)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomerCreateRequestImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.mobileNo, mobileNo) ||
                other.mobileNo == mobileNo) &&
            (identical(other.residentNo, residentNo) ||
                other.residentNo == residentNo) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.city, city) || other.city == city));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, name, mobileNo, residentNo, address, city);

  /// Create a copy of CustomerCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomerCreateRequestImplCopyWith<_$CustomerCreateRequestImpl>
  get copyWith =>
      __$$CustomerCreateRequestImplCopyWithImpl<_$CustomerCreateRequestImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomerCreateRequestImplToJson(this);
  }
}

abstract class _CustomerCreateRequest implements CustomerCreateRequest {
  const factory _CustomerCreateRequest({
    required final String name,
    required final String mobileNo,
    final String? residentNo,
    final String? address,
    final String? city,
  }) = _$CustomerCreateRequestImpl;

  factory _CustomerCreateRequest.fromJson(Map<String, dynamic> json) =
      _$CustomerCreateRequestImpl.fromJson;

  @override
  String get name;
  @override
  String get mobileNo;
  @override
  String? get residentNo;
  @override
  String? get address;
  @override
  String? get city;

  /// Create a copy of CustomerCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomerCreateRequestImplCopyWith<_$CustomerCreateRequestImpl>
  get copyWith => throw _privateConstructorUsedError;
}

CustomerListItem _$CustomerListItemFromJson(Map<String, dynamic> json) {
  return _CustomerListItem.fromJson(json);
}

/// @nodoc
mixin _$CustomerListItem {
  int? get customerId => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get mobileNo => throw _privateConstructorUsedError;
  String? get residentNo => throw _privateConstructorUsedError;
  String? get address => throw _privateConstructorUsedError;
  String? get city => throw _privateConstructorUsedError;
  int? get productCount => throw _privateConstructorUsedError;
  int? get complainCount => throw _privateConstructorUsedError;

  /// Serializes this CustomerListItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomerListItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomerListItemCopyWith<CustomerListItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomerListItemCopyWith<$Res> {
  factory $CustomerListItemCopyWith(
    CustomerListItem value,
    $Res Function(CustomerListItem) then,
  ) = _$CustomerListItemCopyWithImpl<$Res, CustomerListItem>;
  @useResult
  $Res call({
    int? customerId,
    String name,
    String mobileNo,
    String? residentNo,
    String? address,
    String? city,
    int? productCount,
    int? complainCount,
  });
}

/// @nodoc
class _$CustomerListItemCopyWithImpl<$Res, $Val extends CustomerListItem>
    implements $CustomerListItemCopyWith<$Res> {
  _$CustomerListItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomerListItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customerId = freezed,
    Object? name = null,
    Object? mobileNo = null,
    Object? residentNo = freezed,
    Object? address = freezed,
    Object? city = freezed,
    Object? productCount = freezed,
    Object? complainCount = freezed,
  }) {
    return _then(
      _value.copyWith(
            customerId: freezed == customerId
                ? _value.customerId
                : customerId // ignore: cast_nullable_to_non_nullable
                      as int?,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            mobileNo: null == mobileNo
                ? _value.mobileNo
                : mobileNo // ignore: cast_nullable_to_non_nullable
                      as String,
            residentNo: freezed == residentNo
                ? _value.residentNo
                : residentNo // ignore: cast_nullable_to_non_nullable
                      as String?,
            address: freezed == address
                ? _value.address
                : address // ignore: cast_nullable_to_non_nullable
                      as String?,
            city: freezed == city
                ? _value.city
                : city // ignore: cast_nullable_to_non_nullable
                      as String?,
            productCount: freezed == productCount
                ? _value.productCount
                : productCount // ignore: cast_nullable_to_non_nullable
                      as int?,
            complainCount: freezed == complainCount
                ? _value.complainCount
                : complainCount // ignore: cast_nullable_to_non_nullable
                      as int?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CustomerListItemImplCopyWith<$Res>
    implements $CustomerListItemCopyWith<$Res> {
  factory _$$CustomerListItemImplCopyWith(
    _$CustomerListItemImpl value,
    $Res Function(_$CustomerListItemImpl) then,
  ) = __$$CustomerListItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int? customerId,
    String name,
    String mobileNo,
    String? residentNo,
    String? address,
    String? city,
    int? productCount,
    int? complainCount,
  });
}

/// @nodoc
class __$$CustomerListItemImplCopyWithImpl<$Res>
    extends _$CustomerListItemCopyWithImpl<$Res, _$CustomerListItemImpl>
    implements _$$CustomerListItemImplCopyWith<$Res> {
  __$$CustomerListItemImplCopyWithImpl(
    _$CustomerListItemImpl _value,
    $Res Function(_$CustomerListItemImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CustomerListItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customerId = freezed,
    Object? name = null,
    Object? mobileNo = null,
    Object? residentNo = freezed,
    Object? address = freezed,
    Object? city = freezed,
    Object? productCount = freezed,
    Object? complainCount = freezed,
  }) {
    return _then(
      _$CustomerListItemImpl(
        customerId: freezed == customerId
            ? _value.customerId
            : customerId // ignore: cast_nullable_to_non_nullable
                  as int?,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        mobileNo: null == mobileNo
            ? _value.mobileNo
            : mobileNo // ignore: cast_nullable_to_non_nullable
                  as String,
        residentNo: freezed == residentNo
            ? _value.residentNo
            : residentNo // ignore: cast_nullable_to_non_nullable
                  as String?,
        address: freezed == address
            ? _value.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String?,
        city: freezed == city
            ? _value.city
            : city // ignore: cast_nullable_to_non_nullable
                  as String?,
        productCount: freezed == productCount
            ? _value.productCount
            : productCount // ignore: cast_nullable_to_non_nullable
                  as int?,
        complainCount: freezed == complainCount
            ? _value.complainCount
            : complainCount // ignore: cast_nullable_to_non_nullable
                  as int?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomerListItemImpl implements _CustomerListItem {
  const _$CustomerListItemImpl({
    this.customerId,
    required this.name,
    required this.mobileNo,
    this.residentNo,
    this.address,
    this.city,
    this.productCount,
    this.complainCount,
  });

  factory _$CustomerListItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomerListItemImplFromJson(json);

  @override
  final int? customerId;
  @override
  final String name;
  @override
  final String mobileNo;
  @override
  final String? residentNo;
  @override
  final String? address;
  @override
  final String? city;
  @override
  final int? productCount;
  @override
  final int? complainCount;

  @override
  String toString() {
    return 'CustomerListItem(customerId: $customerId, name: $name, mobileNo: $mobileNo, residentNo: $residentNo, address: $address, city: $city, productCount: $productCount, complainCount: $complainCount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomerListItemImpl &&
            (identical(other.customerId, customerId) ||
                other.customerId == customerId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.mobileNo, mobileNo) ||
                other.mobileNo == mobileNo) &&
            (identical(other.residentNo, residentNo) ||
                other.residentNo == residentNo) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.productCount, productCount) ||
                other.productCount == productCount) &&
            (identical(other.complainCount, complainCount) ||
                other.complainCount == complainCount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    customerId,
    name,
    mobileNo,
    residentNo,
    address,
    city,
    productCount,
    complainCount,
  );

  /// Create a copy of CustomerListItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomerListItemImplCopyWith<_$CustomerListItemImpl> get copyWith =>
      __$$CustomerListItemImplCopyWithImpl<_$CustomerListItemImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomerListItemImplToJson(this);
  }
}

abstract class _CustomerListItem implements CustomerListItem {
  const factory _CustomerListItem({
    final int? customerId,
    required final String name,
    required final String mobileNo,
    final String? residentNo,
    final String? address,
    final String? city,
    final int? productCount,
    final int? complainCount,
  }) = _$CustomerListItemImpl;

  factory _CustomerListItem.fromJson(Map<String, dynamic> json) =
      _$CustomerListItemImpl.fromJson;

  @override
  int? get customerId;
  @override
  String get name;
  @override
  String get mobileNo;
  @override
  String? get residentNo;
  @override
  String? get address;
  @override
  String? get city;
  @override
  int? get productCount;
  @override
  int? get complainCount;

  /// Create a copy of CustomerListItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomerListItemImplCopyWith<_$CustomerListItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CustomerSearchResponse _$CustomerSearchResponseFromJson(
  Map<String, dynamic> json,
) {
  return _CustomerSearchResponse.fromJson(json);
}

/// @nodoc
mixin _$CustomerSearchResponse {
  int get responseCode => throw _privateConstructorUsedError;
  String get responseMessage => throw _privateConstructorUsedError;
  List<CustomerListItem>? get response => throw _privateConstructorUsedError;
  bool? get success => throw _privateConstructorUsedError;
  int? get totalElements => throw _privateConstructorUsedError;
  int? get totalPages => throw _privateConstructorUsedError;
  int? get currentPage => throw _privateConstructorUsedError;

  /// Serializes this CustomerSearchResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomerSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomerSearchResponseCopyWith<CustomerSearchResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomerSearchResponseCopyWith<$Res> {
  factory $CustomerSearchResponseCopyWith(
    CustomerSearchResponse value,
    $Res Function(CustomerSearchResponse) then,
  ) = _$CustomerSearchResponseCopyWithImpl<$Res, CustomerSearchResponse>;
  @useResult
  $Res call({
    int responseCode,
    String responseMessage,
    List<CustomerListItem>? response,
    bool? success,
    int? totalElements,
    int? totalPages,
    int? currentPage,
  });
}

/// @nodoc
class _$CustomerSearchResponseCopyWithImpl<
  $Res,
  $Val extends CustomerSearchResponse
>
    implements $CustomerSearchResponseCopyWith<$Res> {
  _$CustomerSearchResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomerSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? responseCode = null,
    Object? responseMessage = null,
    Object? response = freezed,
    Object? success = freezed,
    Object? totalElements = freezed,
    Object? totalPages = freezed,
    Object? currentPage = freezed,
  }) {
    return _then(
      _value.copyWith(
            responseCode: null == responseCode
                ? _value.responseCode
                : responseCode // ignore: cast_nullable_to_non_nullable
                      as int,
            responseMessage: null == responseMessage
                ? _value.responseMessage
                : responseMessage // ignore: cast_nullable_to_non_nullable
                      as String,
            response: freezed == response
                ? _value.response
                : response // ignore: cast_nullable_to_non_nullable
                      as List<CustomerListItem>?,
            success: freezed == success
                ? _value.success
                : success // ignore: cast_nullable_to_non_nullable
                      as bool?,
            totalElements: freezed == totalElements
                ? _value.totalElements
                : totalElements // ignore: cast_nullable_to_non_nullable
                      as int?,
            totalPages: freezed == totalPages
                ? _value.totalPages
                : totalPages // ignore: cast_nullable_to_non_nullable
                      as int?,
            currentPage: freezed == currentPage
                ? _value.currentPage
                : currentPage // ignore: cast_nullable_to_non_nullable
                      as int?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CustomerSearchResponseImplCopyWith<$Res>
    implements $CustomerSearchResponseCopyWith<$Res> {
  factory _$$CustomerSearchResponseImplCopyWith(
    _$CustomerSearchResponseImpl value,
    $Res Function(_$CustomerSearchResponseImpl) then,
  ) = __$$CustomerSearchResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int responseCode,
    String responseMessage,
    List<CustomerListItem>? response,
    bool? success,
    int? totalElements,
    int? totalPages,
    int? currentPage,
  });
}

/// @nodoc
class __$$CustomerSearchResponseImplCopyWithImpl<$Res>
    extends
        _$CustomerSearchResponseCopyWithImpl<$Res, _$CustomerSearchResponseImpl>
    implements _$$CustomerSearchResponseImplCopyWith<$Res> {
  __$$CustomerSearchResponseImplCopyWithImpl(
    _$CustomerSearchResponseImpl _value,
    $Res Function(_$CustomerSearchResponseImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CustomerSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? responseCode = null,
    Object? responseMessage = null,
    Object? response = freezed,
    Object? success = freezed,
    Object? totalElements = freezed,
    Object? totalPages = freezed,
    Object? currentPage = freezed,
  }) {
    return _then(
      _$CustomerSearchResponseImpl(
        responseCode: null == responseCode
            ? _value.responseCode
            : responseCode // ignore: cast_nullable_to_non_nullable
                  as int,
        responseMessage: null == responseMessage
            ? _value.responseMessage
            : responseMessage // ignore: cast_nullable_to_non_nullable
                  as String,
        response: freezed == response
            ? _value._response
            : response // ignore: cast_nullable_to_non_nullable
                  as List<CustomerListItem>?,
        success: freezed == success
            ? _value.success
            : success // ignore: cast_nullable_to_non_nullable
                  as bool?,
        totalElements: freezed == totalElements
            ? _value.totalElements
            : totalElements // ignore: cast_nullable_to_non_nullable
                  as int?,
        totalPages: freezed == totalPages
            ? _value.totalPages
            : totalPages // ignore: cast_nullable_to_non_nullable
                  as int?,
        currentPage: freezed == currentPage
            ? _value.currentPage
            : currentPage // ignore: cast_nullable_to_non_nullable
                  as int?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomerSearchResponseImpl implements _CustomerSearchResponse {
  const _$CustomerSearchResponseImpl({
    required this.responseCode,
    required this.responseMessage,
    final List<CustomerListItem>? response,
    this.success,
    this.totalElements,
    this.totalPages,
    this.currentPage,
  }) : _response = response;

  factory _$CustomerSearchResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomerSearchResponseImplFromJson(json);

  @override
  final int responseCode;
  @override
  final String responseMessage;
  final List<CustomerListItem>? _response;
  @override
  List<CustomerListItem>? get response {
    final value = _response;
    if (value == null) return null;
    if (_response is EqualUnmodifiableListView) return _response;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? success;
  @override
  final int? totalElements;
  @override
  final int? totalPages;
  @override
  final int? currentPage;

  @override
  String toString() {
    return 'CustomerSearchResponse(responseCode: $responseCode, responseMessage: $responseMessage, response: $response, success: $success, totalElements: $totalElements, totalPages: $totalPages, currentPage: $currentPage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomerSearchResponseImpl &&
            (identical(other.responseCode, responseCode) ||
                other.responseCode == responseCode) &&
            (identical(other.responseMessage, responseMessage) ||
                other.responseMessage == responseMessage) &&
            const DeepCollectionEquality().equals(other._response, _response) &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.totalElements, totalElements) ||
                other.totalElements == totalElements) &&
            (identical(other.totalPages, totalPages) ||
                other.totalPages == totalPages) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    responseCode,
    responseMessage,
    const DeepCollectionEquality().hash(_response),
    success,
    totalElements,
    totalPages,
    currentPage,
  );

  /// Create a copy of CustomerSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomerSearchResponseImplCopyWith<_$CustomerSearchResponseImpl>
  get copyWith =>
      __$$CustomerSearchResponseImplCopyWithImpl<_$CustomerSearchResponseImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomerSearchResponseImplToJson(this);
  }
}

abstract class _CustomerSearchResponse implements CustomerSearchResponse {
  const factory _CustomerSearchResponse({
    required final int responseCode,
    required final String responseMessage,
    final List<CustomerListItem>? response,
    final bool? success,
    final int? totalElements,
    final int? totalPages,
    final int? currentPage,
  }) = _$CustomerSearchResponseImpl;

  factory _CustomerSearchResponse.fromJson(Map<String, dynamic> json) =
      _$CustomerSearchResponseImpl.fromJson;

  @override
  int get responseCode;
  @override
  String get responseMessage;
  @override
  List<CustomerListItem>? get response;
  @override
  bool? get success;
  @override
  int? get totalElements;
  @override
  int? get totalPages;
  @override
  int? get currentPage;

  /// Create a copy of CustomerSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomerSearchResponseImplCopyWith<_$CustomerSearchResponseImpl>
  get copyWith => throw _privateConstructorUsedError;
}
