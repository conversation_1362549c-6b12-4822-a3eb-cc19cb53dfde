// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'complaint.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

Complaint _$ComplaintFromJson(Map<String, dynamic> json) {
  return _Complaint.fromJson(json);
}

/// @nodoc
mixin _$Complaint {
  int? get complainId => throw _privateConstructorUsedError;
  String get complainNumber => throw _privateConstructorUsedError;
  String get serviceType => throw _privateConstructorUsedError;
  DateTime get complainOriginDate => throw _privateConstructorUsedError;
  String get complainType => throw _privateConstructorUsedError;
  String get csn => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  Customer? get customer => throw _privateConstructorUsedError;
  Product? get product => throw _privateConstructorUsedError;
  Employee? get assignedEmployee => throw _privateConstructorUsedError;
  DateTime? get registerDate => throw _privateConstructorUsedError;
  DateTime? get closeDate => throw _privateConstructorUsedError;
  String? get closingRemark => throw _privateConstructorUsedError;
  bool? get inOut => throw _privateConstructorUsedError;
  bool? get customerMisuse => throw _privateConstructorUsedError;
  List<Visit>? get visits => throw _privateConstructorUsedError;

  /// Serializes this Complaint to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Complaint
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ComplaintCopyWith<Complaint> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ComplaintCopyWith<$Res> {
  factory $ComplaintCopyWith(Complaint value, $Res Function(Complaint) then) =
      _$ComplaintCopyWithImpl<$Res, Complaint>;
  @useResult
  $Res call({
    int? complainId,
    String complainNumber,
    String serviceType,
    DateTime complainOriginDate,
    String complainType,
    String csn,
    String status,
    Customer? customer,
    Product? product,
    Employee? assignedEmployee,
    DateTime? registerDate,
    DateTime? closeDate,
    String? closingRemark,
    bool? inOut,
    bool? customerMisuse,
    List<Visit>? visits,
  });

  $CustomerCopyWith<$Res>? get customer;
  $ProductCopyWith<$Res>? get product;
  $EmployeeCopyWith<$Res>? get assignedEmployee;
}

/// @nodoc
class _$ComplaintCopyWithImpl<$Res, $Val extends Complaint>
    implements $ComplaintCopyWith<$Res> {
  _$ComplaintCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Complaint
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? complainId = freezed,
    Object? complainNumber = null,
    Object? serviceType = null,
    Object? complainOriginDate = null,
    Object? complainType = null,
    Object? csn = null,
    Object? status = null,
    Object? customer = freezed,
    Object? product = freezed,
    Object? assignedEmployee = freezed,
    Object? registerDate = freezed,
    Object? closeDate = freezed,
    Object? closingRemark = freezed,
    Object? inOut = freezed,
    Object? customerMisuse = freezed,
    Object? visits = freezed,
  }) {
    return _then(
      _value.copyWith(
            complainId: freezed == complainId
                ? _value.complainId
                : complainId // ignore: cast_nullable_to_non_nullable
                      as int?,
            complainNumber: null == complainNumber
                ? _value.complainNumber
                : complainNumber // ignore: cast_nullable_to_non_nullable
                      as String,
            serviceType: null == serviceType
                ? _value.serviceType
                : serviceType // ignore: cast_nullable_to_non_nullable
                      as String,
            complainOriginDate: null == complainOriginDate
                ? _value.complainOriginDate
                : complainOriginDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            complainType: null == complainType
                ? _value.complainType
                : complainType // ignore: cast_nullable_to_non_nullable
                      as String,
            csn: null == csn
                ? _value.csn
                : csn // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as String,
            customer: freezed == customer
                ? _value.customer
                : customer // ignore: cast_nullable_to_non_nullable
                      as Customer?,
            product: freezed == product
                ? _value.product
                : product // ignore: cast_nullable_to_non_nullable
                      as Product?,
            assignedEmployee: freezed == assignedEmployee
                ? _value.assignedEmployee
                : assignedEmployee // ignore: cast_nullable_to_non_nullable
                      as Employee?,
            registerDate: freezed == registerDate
                ? _value.registerDate
                : registerDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            closeDate: freezed == closeDate
                ? _value.closeDate
                : closeDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            closingRemark: freezed == closingRemark
                ? _value.closingRemark
                : closingRemark // ignore: cast_nullable_to_non_nullable
                      as String?,
            inOut: freezed == inOut
                ? _value.inOut
                : inOut // ignore: cast_nullable_to_non_nullable
                      as bool?,
            customerMisuse: freezed == customerMisuse
                ? _value.customerMisuse
                : customerMisuse // ignore: cast_nullable_to_non_nullable
                      as bool?,
            visits: freezed == visits
                ? _value.visits
                : visits // ignore: cast_nullable_to_non_nullable
                      as List<Visit>?,
          )
          as $Val,
    );
  }

  /// Create a copy of Complaint
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomerCopyWith<$Res>? get customer {
    if (_value.customer == null) {
      return null;
    }

    return $CustomerCopyWith<$Res>(_value.customer!, (value) {
      return _then(_value.copyWith(customer: value) as $Val);
    });
  }

  /// Create a copy of Complaint
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductCopyWith<$Res>? get product {
    if (_value.product == null) {
      return null;
    }

    return $ProductCopyWith<$Res>(_value.product!, (value) {
      return _then(_value.copyWith(product: value) as $Val);
    });
  }

  /// Create a copy of Complaint
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EmployeeCopyWith<$Res>? get assignedEmployee {
    if (_value.assignedEmployee == null) {
      return null;
    }

    return $EmployeeCopyWith<$Res>(_value.assignedEmployee!, (value) {
      return _then(_value.copyWith(assignedEmployee: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ComplaintImplCopyWith<$Res>
    implements $ComplaintCopyWith<$Res> {
  factory _$$ComplaintImplCopyWith(
    _$ComplaintImpl value,
    $Res Function(_$ComplaintImpl) then,
  ) = __$$ComplaintImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int? complainId,
    String complainNumber,
    String serviceType,
    DateTime complainOriginDate,
    String complainType,
    String csn,
    String status,
    Customer? customer,
    Product? product,
    Employee? assignedEmployee,
    DateTime? registerDate,
    DateTime? closeDate,
    String? closingRemark,
    bool? inOut,
    bool? customerMisuse,
    List<Visit>? visits,
  });

  @override
  $CustomerCopyWith<$Res>? get customer;
  @override
  $ProductCopyWith<$Res>? get product;
  @override
  $EmployeeCopyWith<$Res>? get assignedEmployee;
}

/// @nodoc
class __$$ComplaintImplCopyWithImpl<$Res>
    extends _$ComplaintCopyWithImpl<$Res, _$ComplaintImpl>
    implements _$$ComplaintImplCopyWith<$Res> {
  __$$ComplaintImplCopyWithImpl(
    _$ComplaintImpl _value,
    $Res Function(_$ComplaintImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Complaint
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? complainId = freezed,
    Object? complainNumber = null,
    Object? serviceType = null,
    Object? complainOriginDate = null,
    Object? complainType = null,
    Object? csn = null,
    Object? status = null,
    Object? customer = freezed,
    Object? product = freezed,
    Object? assignedEmployee = freezed,
    Object? registerDate = freezed,
    Object? closeDate = freezed,
    Object? closingRemark = freezed,
    Object? inOut = freezed,
    Object? customerMisuse = freezed,
    Object? visits = freezed,
  }) {
    return _then(
      _$ComplaintImpl(
        complainId: freezed == complainId
            ? _value.complainId
            : complainId // ignore: cast_nullable_to_non_nullable
                  as int?,
        complainNumber: null == complainNumber
            ? _value.complainNumber
            : complainNumber // ignore: cast_nullable_to_non_nullable
                  as String,
        serviceType: null == serviceType
            ? _value.serviceType
            : serviceType // ignore: cast_nullable_to_non_nullable
                  as String,
        complainOriginDate: null == complainOriginDate
            ? _value.complainOriginDate
            : complainOriginDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        complainType: null == complainType
            ? _value.complainType
            : complainType // ignore: cast_nullable_to_non_nullable
                  as String,
        csn: null == csn
            ? _value.csn
            : csn // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as String,
        customer: freezed == customer
            ? _value.customer
            : customer // ignore: cast_nullable_to_non_nullable
                  as Customer?,
        product: freezed == product
            ? _value.product
            : product // ignore: cast_nullable_to_non_nullable
                  as Product?,
        assignedEmployee: freezed == assignedEmployee
            ? _value.assignedEmployee
            : assignedEmployee // ignore: cast_nullable_to_non_nullable
                  as Employee?,
        registerDate: freezed == registerDate
            ? _value.registerDate
            : registerDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        closeDate: freezed == closeDate
            ? _value.closeDate
            : closeDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        closingRemark: freezed == closingRemark
            ? _value.closingRemark
            : closingRemark // ignore: cast_nullable_to_non_nullable
                  as String?,
        inOut: freezed == inOut
            ? _value.inOut
            : inOut // ignore: cast_nullable_to_non_nullable
                  as bool?,
        customerMisuse: freezed == customerMisuse
            ? _value.customerMisuse
            : customerMisuse // ignore: cast_nullable_to_non_nullable
                  as bool?,
        visits: freezed == visits
            ? _value._visits
            : visits // ignore: cast_nullable_to_non_nullable
                  as List<Visit>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ComplaintImpl implements _Complaint {
  const _$ComplaintImpl({
    this.complainId,
    required this.complainNumber,
    required this.serviceType,
    required this.complainOriginDate,
    required this.complainType,
    required this.csn,
    required this.status,
    this.customer,
    this.product,
    this.assignedEmployee,
    this.registerDate,
    this.closeDate,
    this.closingRemark,
    this.inOut,
    this.customerMisuse,
    final List<Visit>? visits,
  }) : _visits = visits;

  factory _$ComplaintImpl.fromJson(Map<String, dynamic> json) =>
      _$$ComplaintImplFromJson(json);

  @override
  final int? complainId;
  @override
  final String complainNumber;
  @override
  final String serviceType;
  @override
  final DateTime complainOriginDate;
  @override
  final String complainType;
  @override
  final String csn;
  @override
  final String status;
  @override
  final Customer? customer;
  @override
  final Product? product;
  @override
  final Employee? assignedEmployee;
  @override
  final DateTime? registerDate;
  @override
  final DateTime? closeDate;
  @override
  final String? closingRemark;
  @override
  final bool? inOut;
  @override
  final bool? customerMisuse;
  final List<Visit>? _visits;
  @override
  List<Visit>? get visits {
    final value = _visits;
    if (value == null) return null;
    if (_visits is EqualUnmodifiableListView) return _visits;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'Complaint(complainId: $complainId, complainNumber: $complainNumber, serviceType: $serviceType, complainOriginDate: $complainOriginDate, complainType: $complainType, csn: $csn, status: $status, customer: $customer, product: $product, assignedEmployee: $assignedEmployee, registerDate: $registerDate, closeDate: $closeDate, closingRemark: $closingRemark, inOut: $inOut, customerMisuse: $customerMisuse, visits: $visits)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ComplaintImpl &&
            (identical(other.complainId, complainId) ||
                other.complainId == complainId) &&
            (identical(other.complainNumber, complainNumber) ||
                other.complainNumber == complainNumber) &&
            (identical(other.serviceType, serviceType) ||
                other.serviceType == serviceType) &&
            (identical(other.complainOriginDate, complainOriginDate) ||
                other.complainOriginDate == complainOriginDate) &&
            (identical(other.complainType, complainType) ||
                other.complainType == complainType) &&
            (identical(other.csn, csn) || other.csn == csn) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.customer, customer) ||
                other.customer == customer) &&
            (identical(other.product, product) || other.product == product) &&
            (identical(other.assignedEmployee, assignedEmployee) ||
                other.assignedEmployee == assignedEmployee) &&
            (identical(other.registerDate, registerDate) ||
                other.registerDate == registerDate) &&
            (identical(other.closeDate, closeDate) ||
                other.closeDate == closeDate) &&
            (identical(other.closingRemark, closingRemark) ||
                other.closingRemark == closingRemark) &&
            (identical(other.inOut, inOut) || other.inOut == inOut) &&
            (identical(other.customerMisuse, customerMisuse) ||
                other.customerMisuse == customerMisuse) &&
            const DeepCollectionEquality().equals(other._visits, _visits));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    complainId,
    complainNumber,
    serviceType,
    complainOriginDate,
    complainType,
    csn,
    status,
    customer,
    product,
    assignedEmployee,
    registerDate,
    closeDate,
    closingRemark,
    inOut,
    customerMisuse,
    const DeepCollectionEquality().hash(_visits),
  );

  /// Create a copy of Complaint
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ComplaintImplCopyWith<_$ComplaintImpl> get copyWith =>
      __$$ComplaintImplCopyWithImpl<_$ComplaintImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ComplaintImplToJson(this);
  }
}

abstract class _Complaint implements Complaint {
  const factory _Complaint({
    final int? complainId,
    required final String complainNumber,
    required final String serviceType,
    required final DateTime complainOriginDate,
    required final String complainType,
    required final String csn,
    required final String status,
    final Customer? customer,
    final Product? product,
    final Employee? assignedEmployee,
    final DateTime? registerDate,
    final DateTime? closeDate,
    final String? closingRemark,
    final bool? inOut,
    final bool? customerMisuse,
    final List<Visit>? visits,
  }) = _$ComplaintImpl;

  factory _Complaint.fromJson(Map<String, dynamic> json) =
      _$ComplaintImpl.fromJson;

  @override
  int? get complainId;
  @override
  String get complainNumber;
  @override
  String get serviceType;
  @override
  DateTime get complainOriginDate;
  @override
  String get complainType;
  @override
  String get csn;
  @override
  String get status;
  @override
  Customer? get customer;
  @override
  Product? get product;
  @override
  Employee? get assignedEmployee;
  @override
  DateTime? get registerDate;
  @override
  DateTime? get closeDate;
  @override
  String? get closingRemark;
  @override
  bool? get inOut;
  @override
  bool? get customerMisuse;
  @override
  List<Visit>? get visits;

  /// Create a copy of Complaint
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ComplaintImplCopyWith<_$ComplaintImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ComplaintListItem _$ComplaintListItemFromJson(Map<String, dynamic> json) {
  return _ComplaintListItem.fromJson(json);
}

/// @nodoc
mixin _$ComplaintListItem {
  int? get complainId => throw _privateConstructorUsedError;
  String get complainNumber => throw _privateConstructorUsedError;
  String get serviceType => throw _privateConstructorUsedError;
  DateTime get complainOriginDate => throw _privateConstructorUsedError;
  String get complainType => throw _privateConstructorUsedError;
  String get csn => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  String? get customerName => throw _privateConstructorUsedError;
  String? get customerMobile => throw _privateConstructorUsedError;
  String? get productName => throw _privateConstructorUsedError;
  String? get productBrand => throw _privateConstructorUsedError;
  String? get employeeName => throw _privateConstructorUsedError;
  DateTime? get registerDate => throw _privateConstructorUsedError;
  DateTime? get closeDate => throw _privateConstructorUsedError;
  String? get closingRemark => throw _privateConstructorUsedError;
  bool? get inOut => throw _privateConstructorUsedError;
  bool? get customerMisuse => throw _privateConstructorUsedError;

  /// Serializes this ComplaintListItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ComplaintListItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ComplaintListItemCopyWith<ComplaintListItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ComplaintListItemCopyWith<$Res> {
  factory $ComplaintListItemCopyWith(
    ComplaintListItem value,
    $Res Function(ComplaintListItem) then,
  ) = _$ComplaintListItemCopyWithImpl<$Res, ComplaintListItem>;
  @useResult
  $Res call({
    int? complainId,
    String complainNumber,
    String serviceType,
    DateTime complainOriginDate,
    String complainType,
    String csn,
    String status,
    String? customerName,
    String? customerMobile,
    String? productName,
    String? productBrand,
    String? employeeName,
    DateTime? registerDate,
    DateTime? closeDate,
    String? closingRemark,
    bool? inOut,
    bool? customerMisuse,
  });
}

/// @nodoc
class _$ComplaintListItemCopyWithImpl<$Res, $Val extends ComplaintListItem>
    implements $ComplaintListItemCopyWith<$Res> {
  _$ComplaintListItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ComplaintListItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? complainId = freezed,
    Object? complainNumber = null,
    Object? serviceType = null,
    Object? complainOriginDate = null,
    Object? complainType = null,
    Object? csn = null,
    Object? status = null,
    Object? customerName = freezed,
    Object? customerMobile = freezed,
    Object? productName = freezed,
    Object? productBrand = freezed,
    Object? employeeName = freezed,
    Object? registerDate = freezed,
    Object? closeDate = freezed,
    Object? closingRemark = freezed,
    Object? inOut = freezed,
    Object? customerMisuse = freezed,
  }) {
    return _then(
      _value.copyWith(
            complainId: freezed == complainId
                ? _value.complainId
                : complainId // ignore: cast_nullable_to_non_nullable
                      as int?,
            complainNumber: null == complainNumber
                ? _value.complainNumber
                : complainNumber // ignore: cast_nullable_to_non_nullable
                      as String,
            serviceType: null == serviceType
                ? _value.serviceType
                : serviceType // ignore: cast_nullable_to_non_nullable
                      as String,
            complainOriginDate: null == complainOriginDate
                ? _value.complainOriginDate
                : complainOriginDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            complainType: null == complainType
                ? _value.complainType
                : complainType // ignore: cast_nullable_to_non_nullable
                      as String,
            csn: null == csn
                ? _value.csn
                : csn // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as String,
            customerName: freezed == customerName
                ? _value.customerName
                : customerName // ignore: cast_nullable_to_non_nullable
                      as String?,
            customerMobile: freezed == customerMobile
                ? _value.customerMobile
                : customerMobile // ignore: cast_nullable_to_non_nullable
                      as String?,
            productName: freezed == productName
                ? _value.productName
                : productName // ignore: cast_nullable_to_non_nullable
                      as String?,
            productBrand: freezed == productBrand
                ? _value.productBrand
                : productBrand // ignore: cast_nullable_to_non_nullable
                      as String?,
            employeeName: freezed == employeeName
                ? _value.employeeName
                : employeeName // ignore: cast_nullable_to_non_nullable
                      as String?,
            registerDate: freezed == registerDate
                ? _value.registerDate
                : registerDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            closeDate: freezed == closeDate
                ? _value.closeDate
                : closeDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            closingRemark: freezed == closingRemark
                ? _value.closingRemark
                : closingRemark // ignore: cast_nullable_to_non_nullable
                      as String?,
            inOut: freezed == inOut
                ? _value.inOut
                : inOut // ignore: cast_nullable_to_non_nullable
                      as bool?,
            customerMisuse: freezed == customerMisuse
                ? _value.customerMisuse
                : customerMisuse // ignore: cast_nullable_to_non_nullable
                      as bool?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ComplaintListItemImplCopyWith<$Res>
    implements $ComplaintListItemCopyWith<$Res> {
  factory _$$ComplaintListItemImplCopyWith(
    _$ComplaintListItemImpl value,
    $Res Function(_$ComplaintListItemImpl) then,
  ) = __$$ComplaintListItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int? complainId,
    String complainNumber,
    String serviceType,
    DateTime complainOriginDate,
    String complainType,
    String csn,
    String status,
    String? customerName,
    String? customerMobile,
    String? productName,
    String? productBrand,
    String? employeeName,
    DateTime? registerDate,
    DateTime? closeDate,
    String? closingRemark,
    bool? inOut,
    bool? customerMisuse,
  });
}

/// @nodoc
class __$$ComplaintListItemImplCopyWithImpl<$Res>
    extends _$ComplaintListItemCopyWithImpl<$Res, _$ComplaintListItemImpl>
    implements _$$ComplaintListItemImplCopyWith<$Res> {
  __$$ComplaintListItemImplCopyWithImpl(
    _$ComplaintListItemImpl _value,
    $Res Function(_$ComplaintListItemImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ComplaintListItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? complainId = freezed,
    Object? complainNumber = null,
    Object? serviceType = null,
    Object? complainOriginDate = null,
    Object? complainType = null,
    Object? csn = null,
    Object? status = null,
    Object? customerName = freezed,
    Object? customerMobile = freezed,
    Object? productName = freezed,
    Object? productBrand = freezed,
    Object? employeeName = freezed,
    Object? registerDate = freezed,
    Object? closeDate = freezed,
    Object? closingRemark = freezed,
    Object? inOut = freezed,
    Object? customerMisuse = freezed,
  }) {
    return _then(
      _$ComplaintListItemImpl(
        complainId: freezed == complainId
            ? _value.complainId
            : complainId // ignore: cast_nullable_to_non_nullable
                  as int?,
        complainNumber: null == complainNumber
            ? _value.complainNumber
            : complainNumber // ignore: cast_nullable_to_non_nullable
                  as String,
        serviceType: null == serviceType
            ? _value.serviceType
            : serviceType // ignore: cast_nullable_to_non_nullable
                  as String,
        complainOriginDate: null == complainOriginDate
            ? _value.complainOriginDate
            : complainOriginDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        complainType: null == complainType
            ? _value.complainType
            : complainType // ignore: cast_nullable_to_non_nullable
                  as String,
        csn: null == csn
            ? _value.csn
            : csn // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as String,
        customerName: freezed == customerName
            ? _value.customerName
            : customerName // ignore: cast_nullable_to_non_nullable
                  as String?,
        customerMobile: freezed == customerMobile
            ? _value.customerMobile
            : customerMobile // ignore: cast_nullable_to_non_nullable
                  as String?,
        productName: freezed == productName
            ? _value.productName
            : productName // ignore: cast_nullable_to_non_nullable
                  as String?,
        productBrand: freezed == productBrand
            ? _value.productBrand
            : productBrand // ignore: cast_nullable_to_non_nullable
                  as String?,
        employeeName: freezed == employeeName
            ? _value.employeeName
            : employeeName // ignore: cast_nullable_to_non_nullable
                  as String?,
        registerDate: freezed == registerDate
            ? _value.registerDate
            : registerDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        closeDate: freezed == closeDate
            ? _value.closeDate
            : closeDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        closingRemark: freezed == closingRemark
            ? _value.closingRemark
            : closingRemark // ignore: cast_nullable_to_non_nullable
                  as String?,
        inOut: freezed == inOut
            ? _value.inOut
            : inOut // ignore: cast_nullable_to_non_nullable
                  as bool?,
        customerMisuse: freezed == customerMisuse
            ? _value.customerMisuse
            : customerMisuse // ignore: cast_nullable_to_non_nullable
                  as bool?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ComplaintListItemImpl implements _ComplaintListItem {
  const _$ComplaintListItemImpl({
    this.complainId,
    required this.complainNumber,
    required this.serviceType,
    required this.complainOriginDate,
    required this.complainType,
    required this.csn,
    required this.status,
    this.customerName,
    this.customerMobile,
    this.productName,
    this.productBrand,
    this.employeeName,
    this.registerDate,
    this.closeDate,
    this.closingRemark,
    this.inOut,
    this.customerMisuse,
  });

  factory _$ComplaintListItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$ComplaintListItemImplFromJson(json);

  @override
  final int? complainId;
  @override
  final String complainNumber;
  @override
  final String serviceType;
  @override
  final DateTime complainOriginDate;
  @override
  final String complainType;
  @override
  final String csn;
  @override
  final String status;
  @override
  final String? customerName;
  @override
  final String? customerMobile;
  @override
  final String? productName;
  @override
  final String? productBrand;
  @override
  final String? employeeName;
  @override
  final DateTime? registerDate;
  @override
  final DateTime? closeDate;
  @override
  final String? closingRemark;
  @override
  final bool? inOut;
  @override
  final bool? customerMisuse;

  @override
  String toString() {
    return 'ComplaintListItem(complainId: $complainId, complainNumber: $complainNumber, serviceType: $serviceType, complainOriginDate: $complainOriginDate, complainType: $complainType, csn: $csn, status: $status, customerName: $customerName, customerMobile: $customerMobile, productName: $productName, productBrand: $productBrand, employeeName: $employeeName, registerDate: $registerDate, closeDate: $closeDate, closingRemark: $closingRemark, inOut: $inOut, customerMisuse: $customerMisuse)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ComplaintListItemImpl &&
            (identical(other.complainId, complainId) ||
                other.complainId == complainId) &&
            (identical(other.complainNumber, complainNumber) ||
                other.complainNumber == complainNumber) &&
            (identical(other.serviceType, serviceType) ||
                other.serviceType == serviceType) &&
            (identical(other.complainOriginDate, complainOriginDate) ||
                other.complainOriginDate == complainOriginDate) &&
            (identical(other.complainType, complainType) ||
                other.complainType == complainType) &&
            (identical(other.csn, csn) || other.csn == csn) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.customerName, customerName) ||
                other.customerName == customerName) &&
            (identical(other.customerMobile, customerMobile) ||
                other.customerMobile == customerMobile) &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.productBrand, productBrand) ||
                other.productBrand == productBrand) &&
            (identical(other.employeeName, employeeName) ||
                other.employeeName == employeeName) &&
            (identical(other.registerDate, registerDate) ||
                other.registerDate == registerDate) &&
            (identical(other.closeDate, closeDate) ||
                other.closeDate == closeDate) &&
            (identical(other.closingRemark, closingRemark) ||
                other.closingRemark == closingRemark) &&
            (identical(other.inOut, inOut) || other.inOut == inOut) &&
            (identical(other.customerMisuse, customerMisuse) ||
                other.customerMisuse == customerMisuse));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    complainId,
    complainNumber,
    serviceType,
    complainOriginDate,
    complainType,
    csn,
    status,
    customerName,
    customerMobile,
    productName,
    productBrand,
    employeeName,
    registerDate,
    closeDate,
    closingRemark,
    inOut,
    customerMisuse,
  );

  /// Create a copy of ComplaintListItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ComplaintListItemImplCopyWith<_$ComplaintListItemImpl> get copyWith =>
      __$$ComplaintListItemImplCopyWithImpl<_$ComplaintListItemImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ComplaintListItemImplToJson(this);
  }
}

abstract class _ComplaintListItem implements ComplaintListItem {
  const factory _ComplaintListItem({
    final int? complainId,
    required final String complainNumber,
    required final String serviceType,
    required final DateTime complainOriginDate,
    required final String complainType,
    required final String csn,
    required final String status,
    final String? customerName,
    final String? customerMobile,
    final String? productName,
    final String? productBrand,
    final String? employeeName,
    final DateTime? registerDate,
    final DateTime? closeDate,
    final String? closingRemark,
    final bool? inOut,
    final bool? customerMisuse,
  }) = _$ComplaintListItemImpl;

  factory _ComplaintListItem.fromJson(Map<String, dynamic> json) =
      _$ComplaintListItemImpl.fromJson;

  @override
  int? get complainId;
  @override
  String get complainNumber;
  @override
  String get serviceType;
  @override
  DateTime get complainOriginDate;
  @override
  String get complainType;
  @override
  String get csn;
  @override
  String get status;
  @override
  String? get customerName;
  @override
  String? get customerMobile;
  @override
  String? get productName;
  @override
  String? get productBrand;
  @override
  String? get employeeName;
  @override
  DateTime? get registerDate;
  @override
  DateTime? get closeDate;
  @override
  String? get closingRemark;
  @override
  bool? get inOut;
  @override
  bool? get customerMisuse;

  /// Create a copy of ComplaintListItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ComplaintListItemImplCopyWith<_$ComplaintListItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ComplaintCreateRequest _$ComplaintCreateRequestFromJson(
  Map<String, dynamic> json,
) {
  return _ComplaintCreateRequest.fromJson(json);
}

/// @nodoc
mixin _$ComplaintCreateRequest {
  CustomerCreateRequest get customerCreateRequest =>
      throw _privateConstructorUsedError;
  ProductCreateRequest get productCreateRequest =>
      throw _privateConstructorUsedError;
  ComplaintRegisterRequest get complainRegisterRequest =>
      throw _privateConstructorUsedError;

  /// Serializes this ComplaintCreateRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ComplaintCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ComplaintCreateRequestCopyWith<ComplaintCreateRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ComplaintCreateRequestCopyWith<$Res> {
  factory $ComplaintCreateRequestCopyWith(
    ComplaintCreateRequest value,
    $Res Function(ComplaintCreateRequest) then,
  ) = _$ComplaintCreateRequestCopyWithImpl<$Res, ComplaintCreateRequest>;
  @useResult
  $Res call({
    CustomerCreateRequest customerCreateRequest,
    ProductCreateRequest productCreateRequest,
    ComplaintRegisterRequest complainRegisterRequest,
  });

  $CustomerCreateRequestCopyWith<$Res> get customerCreateRequest;
  $ProductCreateRequestCopyWith<$Res> get productCreateRequest;
  $ComplaintRegisterRequestCopyWith<$Res> get complainRegisterRequest;
}

/// @nodoc
class _$ComplaintCreateRequestCopyWithImpl<
  $Res,
  $Val extends ComplaintCreateRequest
>
    implements $ComplaintCreateRequestCopyWith<$Res> {
  _$ComplaintCreateRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ComplaintCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customerCreateRequest = null,
    Object? productCreateRequest = null,
    Object? complainRegisterRequest = null,
  }) {
    return _then(
      _value.copyWith(
            customerCreateRequest: null == customerCreateRequest
                ? _value.customerCreateRequest
                : customerCreateRequest // ignore: cast_nullable_to_non_nullable
                      as CustomerCreateRequest,
            productCreateRequest: null == productCreateRequest
                ? _value.productCreateRequest
                : productCreateRequest // ignore: cast_nullable_to_non_nullable
                      as ProductCreateRequest,
            complainRegisterRequest: null == complainRegisterRequest
                ? _value.complainRegisterRequest
                : complainRegisterRequest // ignore: cast_nullable_to_non_nullable
                      as ComplaintRegisterRequest,
          )
          as $Val,
    );
  }

  /// Create a copy of ComplaintCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomerCreateRequestCopyWith<$Res> get customerCreateRequest {
    return $CustomerCreateRequestCopyWith<$Res>(_value.customerCreateRequest, (
      value,
    ) {
      return _then(_value.copyWith(customerCreateRequest: value) as $Val);
    });
  }

  /// Create a copy of ComplaintCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductCreateRequestCopyWith<$Res> get productCreateRequest {
    return $ProductCreateRequestCopyWith<$Res>(_value.productCreateRequest, (
      value,
    ) {
      return _then(_value.copyWith(productCreateRequest: value) as $Val);
    });
  }

  /// Create a copy of ComplaintCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ComplaintRegisterRequestCopyWith<$Res> get complainRegisterRequest {
    return $ComplaintRegisterRequestCopyWith<$Res>(
      _value.complainRegisterRequest,
      (value) {
        return _then(_value.copyWith(complainRegisterRequest: value) as $Val);
      },
    );
  }
}

/// @nodoc
abstract class _$$ComplaintCreateRequestImplCopyWith<$Res>
    implements $ComplaintCreateRequestCopyWith<$Res> {
  factory _$$ComplaintCreateRequestImplCopyWith(
    _$ComplaintCreateRequestImpl value,
    $Res Function(_$ComplaintCreateRequestImpl) then,
  ) = __$$ComplaintCreateRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    CustomerCreateRequest customerCreateRequest,
    ProductCreateRequest productCreateRequest,
    ComplaintRegisterRequest complainRegisterRequest,
  });

  @override
  $CustomerCreateRequestCopyWith<$Res> get customerCreateRequest;
  @override
  $ProductCreateRequestCopyWith<$Res> get productCreateRequest;
  @override
  $ComplaintRegisterRequestCopyWith<$Res> get complainRegisterRequest;
}

/// @nodoc
class __$$ComplaintCreateRequestImplCopyWithImpl<$Res>
    extends
        _$ComplaintCreateRequestCopyWithImpl<$Res, _$ComplaintCreateRequestImpl>
    implements _$$ComplaintCreateRequestImplCopyWith<$Res> {
  __$$ComplaintCreateRequestImplCopyWithImpl(
    _$ComplaintCreateRequestImpl _value,
    $Res Function(_$ComplaintCreateRequestImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ComplaintCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customerCreateRequest = null,
    Object? productCreateRequest = null,
    Object? complainRegisterRequest = null,
  }) {
    return _then(
      _$ComplaintCreateRequestImpl(
        customerCreateRequest: null == customerCreateRequest
            ? _value.customerCreateRequest
            : customerCreateRequest // ignore: cast_nullable_to_non_nullable
                  as CustomerCreateRequest,
        productCreateRequest: null == productCreateRequest
            ? _value.productCreateRequest
            : productCreateRequest // ignore: cast_nullable_to_non_nullable
                  as ProductCreateRequest,
        complainRegisterRequest: null == complainRegisterRequest
            ? _value.complainRegisterRequest
            : complainRegisterRequest // ignore: cast_nullable_to_non_nullable
                  as ComplaintRegisterRequest,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ComplaintCreateRequestImpl implements _ComplaintCreateRequest {
  const _$ComplaintCreateRequestImpl({
    required this.customerCreateRequest,
    required this.productCreateRequest,
    required this.complainRegisterRequest,
  });

  factory _$ComplaintCreateRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$ComplaintCreateRequestImplFromJson(json);

  @override
  final CustomerCreateRequest customerCreateRequest;
  @override
  final ProductCreateRequest productCreateRequest;
  @override
  final ComplaintRegisterRequest complainRegisterRequest;

  @override
  String toString() {
    return 'ComplaintCreateRequest(customerCreateRequest: $customerCreateRequest, productCreateRequest: $productCreateRequest, complainRegisterRequest: $complainRegisterRequest)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ComplaintCreateRequestImpl &&
            (identical(other.customerCreateRequest, customerCreateRequest) ||
                other.customerCreateRequest == customerCreateRequest) &&
            (identical(other.productCreateRequest, productCreateRequest) ||
                other.productCreateRequest == productCreateRequest) &&
            (identical(
                  other.complainRegisterRequest,
                  complainRegisterRequest,
                ) ||
                other.complainRegisterRequest == complainRegisterRequest));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    customerCreateRequest,
    productCreateRequest,
    complainRegisterRequest,
  );

  /// Create a copy of ComplaintCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ComplaintCreateRequestImplCopyWith<_$ComplaintCreateRequestImpl>
  get copyWith =>
      __$$ComplaintCreateRequestImplCopyWithImpl<_$ComplaintCreateRequestImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ComplaintCreateRequestImplToJson(this);
  }
}

abstract class _ComplaintCreateRequest implements ComplaintCreateRequest {
  const factory _ComplaintCreateRequest({
    required final CustomerCreateRequest customerCreateRequest,
    required final ProductCreateRequest productCreateRequest,
    required final ComplaintRegisterRequest complainRegisterRequest,
  }) = _$ComplaintCreateRequestImpl;

  factory _ComplaintCreateRequest.fromJson(Map<String, dynamic> json) =
      _$ComplaintCreateRequestImpl.fromJson;

  @override
  CustomerCreateRequest get customerCreateRequest;
  @override
  ProductCreateRequest get productCreateRequest;
  @override
  ComplaintRegisterRequest get complainRegisterRequest;

  /// Create a copy of ComplaintCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ComplaintCreateRequestImplCopyWith<_$ComplaintCreateRequestImpl>
  get copyWith => throw _privateConstructorUsedError;
}

ComplaintRegisterRequest _$ComplaintRegisterRequestFromJson(
  Map<String, dynamic> json,
) {
  return _ComplaintRegisterRequest.fromJson(json);
}

/// @nodoc
mixin _$ComplaintRegisterRequest {
  String get complainNumber => throw _privateConstructorUsedError;
  String get serviceType => throw _privateConstructorUsedError;
  DateTime get complainOriginDate => throw _privateConstructorUsedError;
  String get complainType => throw _privateConstructorUsedError;
  String get csn => throw _privateConstructorUsedError;
  bool? get inOut => throw _privateConstructorUsedError;
  bool? get customerMisuse => throw _privateConstructorUsedError;

  /// Serializes this ComplaintRegisterRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ComplaintRegisterRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ComplaintRegisterRequestCopyWith<ComplaintRegisterRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ComplaintRegisterRequestCopyWith<$Res> {
  factory $ComplaintRegisterRequestCopyWith(
    ComplaintRegisterRequest value,
    $Res Function(ComplaintRegisterRequest) then,
  ) = _$ComplaintRegisterRequestCopyWithImpl<$Res, ComplaintRegisterRequest>;
  @useResult
  $Res call({
    String complainNumber,
    String serviceType,
    DateTime complainOriginDate,
    String complainType,
    String csn,
    bool? inOut,
    bool? customerMisuse,
  });
}

/// @nodoc
class _$ComplaintRegisterRequestCopyWithImpl<
  $Res,
  $Val extends ComplaintRegisterRequest
>
    implements $ComplaintRegisterRequestCopyWith<$Res> {
  _$ComplaintRegisterRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ComplaintRegisterRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? complainNumber = null,
    Object? serviceType = null,
    Object? complainOriginDate = null,
    Object? complainType = null,
    Object? csn = null,
    Object? inOut = freezed,
    Object? customerMisuse = freezed,
  }) {
    return _then(
      _value.copyWith(
            complainNumber: null == complainNumber
                ? _value.complainNumber
                : complainNumber // ignore: cast_nullable_to_non_nullable
                      as String,
            serviceType: null == serviceType
                ? _value.serviceType
                : serviceType // ignore: cast_nullable_to_non_nullable
                      as String,
            complainOriginDate: null == complainOriginDate
                ? _value.complainOriginDate
                : complainOriginDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            complainType: null == complainType
                ? _value.complainType
                : complainType // ignore: cast_nullable_to_non_nullable
                      as String,
            csn: null == csn
                ? _value.csn
                : csn // ignore: cast_nullable_to_non_nullable
                      as String,
            inOut: freezed == inOut
                ? _value.inOut
                : inOut // ignore: cast_nullable_to_non_nullable
                      as bool?,
            customerMisuse: freezed == customerMisuse
                ? _value.customerMisuse
                : customerMisuse // ignore: cast_nullable_to_non_nullable
                      as bool?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ComplaintRegisterRequestImplCopyWith<$Res>
    implements $ComplaintRegisterRequestCopyWith<$Res> {
  factory _$$ComplaintRegisterRequestImplCopyWith(
    _$ComplaintRegisterRequestImpl value,
    $Res Function(_$ComplaintRegisterRequestImpl) then,
  ) = __$$ComplaintRegisterRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String complainNumber,
    String serviceType,
    DateTime complainOriginDate,
    String complainType,
    String csn,
    bool? inOut,
    bool? customerMisuse,
  });
}

/// @nodoc
class __$$ComplaintRegisterRequestImplCopyWithImpl<$Res>
    extends
        _$ComplaintRegisterRequestCopyWithImpl<
          $Res,
          _$ComplaintRegisterRequestImpl
        >
    implements _$$ComplaintRegisterRequestImplCopyWith<$Res> {
  __$$ComplaintRegisterRequestImplCopyWithImpl(
    _$ComplaintRegisterRequestImpl _value,
    $Res Function(_$ComplaintRegisterRequestImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ComplaintRegisterRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? complainNumber = null,
    Object? serviceType = null,
    Object? complainOriginDate = null,
    Object? complainType = null,
    Object? csn = null,
    Object? inOut = freezed,
    Object? customerMisuse = freezed,
  }) {
    return _then(
      _$ComplaintRegisterRequestImpl(
        complainNumber: null == complainNumber
            ? _value.complainNumber
            : complainNumber // ignore: cast_nullable_to_non_nullable
                  as String,
        serviceType: null == serviceType
            ? _value.serviceType
            : serviceType // ignore: cast_nullable_to_non_nullable
                  as String,
        complainOriginDate: null == complainOriginDate
            ? _value.complainOriginDate
            : complainOriginDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        complainType: null == complainType
            ? _value.complainType
            : complainType // ignore: cast_nullable_to_non_nullable
                  as String,
        csn: null == csn
            ? _value.csn
            : csn // ignore: cast_nullable_to_non_nullable
                  as String,
        inOut: freezed == inOut
            ? _value.inOut
            : inOut // ignore: cast_nullable_to_non_nullable
                  as bool?,
        customerMisuse: freezed == customerMisuse
            ? _value.customerMisuse
            : customerMisuse // ignore: cast_nullable_to_non_nullable
                  as bool?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ComplaintRegisterRequestImpl implements _ComplaintRegisterRequest {
  const _$ComplaintRegisterRequestImpl({
    required this.complainNumber,
    required this.serviceType,
    required this.complainOriginDate,
    required this.complainType,
    required this.csn,
    this.inOut,
    this.customerMisuse,
  });

  factory _$ComplaintRegisterRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$ComplaintRegisterRequestImplFromJson(json);

  @override
  final String complainNumber;
  @override
  final String serviceType;
  @override
  final DateTime complainOriginDate;
  @override
  final String complainType;
  @override
  final String csn;
  @override
  final bool? inOut;
  @override
  final bool? customerMisuse;

  @override
  String toString() {
    return 'ComplaintRegisterRequest(complainNumber: $complainNumber, serviceType: $serviceType, complainOriginDate: $complainOriginDate, complainType: $complainType, csn: $csn, inOut: $inOut, customerMisuse: $customerMisuse)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ComplaintRegisterRequestImpl &&
            (identical(other.complainNumber, complainNumber) ||
                other.complainNumber == complainNumber) &&
            (identical(other.serviceType, serviceType) ||
                other.serviceType == serviceType) &&
            (identical(other.complainOriginDate, complainOriginDate) ||
                other.complainOriginDate == complainOriginDate) &&
            (identical(other.complainType, complainType) ||
                other.complainType == complainType) &&
            (identical(other.csn, csn) || other.csn == csn) &&
            (identical(other.inOut, inOut) || other.inOut == inOut) &&
            (identical(other.customerMisuse, customerMisuse) ||
                other.customerMisuse == customerMisuse));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    complainNumber,
    serviceType,
    complainOriginDate,
    complainType,
    csn,
    inOut,
    customerMisuse,
  );

  /// Create a copy of ComplaintRegisterRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ComplaintRegisterRequestImplCopyWith<_$ComplaintRegisterRequestImpl>
  get copyWith =>
      __$$ComplaintRegisterRequestImplCopyWithImpl<
        _$ComplaintRegisterRequestImpl
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ComplaintRegisterRequestImplToJson(this);
  }
}

abstract class _ComplaintRegisterRequest implements ComplaintRegisterRequest {
  const factory _ComplaintRegisterRequest({
    required final String complainNumber,
    required final String serviceType,
    required final DateTime complainOriginDate,
    required final String complainType,
    required final String csn,
    final bool? inOut,
    final bool? customerMisuse,
  }) = _$ComplaintRegisterRequestImpl;

  factory _ComplaintRegisterRequest.fromJson(Map<String, dynamic> json) =
      _$ComplaintRegisterRequestImpl.fromJson;

  @override
  String get complainNumber;
  @override
  String get serviceType;
  @override
  DateTime get complainOriginDate;
  @override
  String get complainType;
  @override
  String get csn;
  @override
  bool? get inOut;
  @override
  bool? get customerMisuse;

  /// Create a copy of ComplaintRegisterRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ComplaintRegisterRequestImplCopyWith<_$ComplaintRegisterRequestImpl>
  get copyWith => throw _privateConstructorUsedError;
}

ComplaintSearchRequest _$ComplaintSearchRequestFromJson(
  Map<String, dynamic> json,
) {
  return _ComplaintSearchRequest.fromJson(json);
}

/// @nodoc
mixin _$ComplaintSearchRequest {
  String? get complainNumber => throw _privateConstructorUsedError;
  String? get customerName => throw _privateConstructorUsedError;
  String? get customerMobile => throw _privateConstructorUsedError;
  String? get status => throw _privateConstructorUsedError;
  DateTime? get fromDate => throw _privateConstructorUsedError;
  DateTime? get toDate => throw _privateConstructorUsedError;
  int? get page => throw _privateConstructorUsedError;
  int? get size => throw _privateConstructorUsedError;
  String? get sortBy => throw _privateConstructorUsedError;
  String? get sortDirection => throw _privateConstructorUsedError;

  /// Serializes this ComplaintSearchRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ComplaintSearchRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ComplaintSearchRequestCopyWith<ComplaintSearchRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ComplaintSearchRequestCopyWith<$Res> {
  factory $ComplaintSearchRequestCopyWith(
    ComplaintSearchRequest value,
    $Res Function(ComplaintSearchRequest) then,
  ) = _$ComplaintSearchRequestCopyWithImpl<$Res, ComplaintSearchRequest>;
  @useResult
  $Res call({
    String? complainNumber,
    String? customerName,
    String? customerMobile,
    String? status,
    DateTime? fromDate,
    DateTime? toDate,
    int? page,
    int? size,
    String? sortBy,
    String? sortDirection,
  });
}

/// @nodoc
class _$ComplaintSearchRequestCopyWithImpl<
  $Res,
  $Val extends ComplaintSearchRequest
>
    implements $ComplaintSearchRequestCopyWith<$Res> {
  _$ComplaintSearchRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ComplaintSearchRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? complainNumber = freezed,
    Object? customerName = freezed,
    Object? customerMobile = freezed,
    Object? status = freezed,
    Object? fromDate = freezed,
    Object? toDate = freezed,
    Object? page = freezed,
    Object? size = freezed,
    Object? sortBy = freezed,
    Object? sortDirection = freezed,
  }) {
    return _then(
      _value.copyWith(
            complainNumber: freezed == complainNumber
                ? _value.complainNumber
                : complainNumber // ignore: cast_nullable_to_non_nullable
                      as String?,
            customerName: freezed == customerName
                ? _value.customerName
                : customerName // ignore: cast_nullable_to_non_nullable
                      as String?,
            customerMobile: freezed == customerMobile
                ? _value.customerMobile
                : customerMobile // ignore: cast_nullable_to_non_nullable
                      as String?,
            status: freezed == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as String?,
            fromDate: freezed == fromDate
                ? _value.fromDate
                : fromDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            toDate: freezed == toDate
                ? _value.toDate
                : toDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            page: freezed == page
                ? _value.page
                : page // ignore: cast_nullable_to_non_nullable
                      as int?,
            size: freezed == size
                ? _value.size
                : size // ignore: cast_nullable_to_non_nullable
                      as int?,
            sortBy: freezed == sortBy
                ? _value.sortBy
                : sortBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            sortDirection: freezed == sortDirection
                ? _value.sortDirection
                : sortDirection // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ComplaintSearchRequestImplCopyWith<$Res>
    implements $ComplaintSearchRequestCopyWith<$Res> {
  factory _$$ComplaintSearchRequestImplCopyWith(
    _$ComplaintSearchRequestImpl value,
    $Res Function(_$ComplaintSearchRequestImpl) then,
  ) = __$$ComplaintSearchRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String? complainNumber,
    String? customerName,
    String? customerMobile,
    String? status,
    DateTime? fromDate,
    DateTime? toDate,
    int? page,
    int? size,
    String? sortBy,
    String? sortDirection,
  });
}

/// @nodoc
class __$$ComplaintSearchRequestImplCopyWithImpl<$Res>
    extends
        _$ComplaintSearchRequestCopyWithImpl<$Res, _$ComplaintSearchRequestImpl>
    implements _$$ComplaintSearchRequestImplCopyWith<$Res> {
  __$$ComplaintSearchRequestImplCopyWithImpl(
    _$ComplaintSearchRequestImpl _value,
    $Res Function(_$ComplaintSearchRequestImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ComplaintSearchRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? complainNumber = freezed,
    Object? customerName = freezed,
    Object? customerMobile = freezed,
    Object? status = freezed,
    Object? fromDate = freezed,
    Object? toDate = freezed,
    Object? page = freezed,
    Object? size = freezed,
    Object? sortBy = freezed,
    Object? sortDirection = freezed,
  }) {
    return _then(
      _$ComplaintSearchRequestImpl(
        complainNumber: freezed == complainNumber
            ? _value.complainNumber
            : complainNumber // ignore: cast_nullable_to_non_nullable
                  as String?,
        customerName: freezed == customerName
            ? _value.customerName
            : customerName // ignore: cast_nullable_to_non_nullable
                  as String?,
        customerMobile: freezed == customerMobile
            ? _value.customerMobile
            : customerMobile // ignore: cast_nullable_to_non_nullable
                  as String?,
        status: freezed == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as String?,
        fromDate: freezed == fromDate
            ? _value.fromDate
            : fromDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        toDate: freezed == toDate
            ? _value.toDate
            : toDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        page: freezed == page
            ? _value.page
            : page // ignore: cast_nullable_to_non_nullable
                  as int?,
        size: freezed == size
            ? _value.size
            : size // ignore: cast_nullable_to_non_nullable
                  as int?,
        sortBy: freezed == sortBy
            ? _value.sortBy
            : sortBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        sortDirection: freezed == sortDirection
            ? _value.sortDirection
            : sortDirection // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ComplaintSearchRequestImpl implements _ComplaintSearchRequest {
  const _$ComplaintSearchRequestImpl({
    this.complainNumber,
    this.customerName,
    this.customerMobile,
    this.status,
    this.fromDate,
    this.toDate,
    this.page,
    this.size,
    this.sortBy,
    this.sortDirection,
  });

  factory _$ComplaintSearchRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$ComplaintSearchRequestImplFromJson(json);

  @override
  final String? complainNumber;
  @override
  final String? customerName;
  @override
  final String? customerMobile;
  @override
  final String? status;
  @override
  final DateTime? fromDate;
  @override
  final DateTime? toDate;
  @override
  final int? page;
  @override
  final int? size;
  @override
  final String? sortBy;
  @override
  final String? sortDirection;

  @override
  String toString() {
    return 'ComplaintSearchRequest(complainNumber: $complainNumber, customerName: $customerName, customerMobile: $customerMobile, status: $status, fromDate: $fromDate, toDate: $toDate, page: $page, size: $size, sortBy: $sortBy, sortDirection: $sortDirection)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ComplaintSearchRequestImpl &&
            (identical(other.complainNumber, complainNumber) ||
                other.complainNumber == complainNumber) &&
            (identical(other.customerName, customerName) ||
                other.customerName == customerName) &&
            (identical(other.customerMobile, customerMobile) ||
                other.customerMobile == customerMobile) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.fromDate, fromDate) ||
                other.fromDate == fromDate) &&
            (identical(other.toDate, toDate) || other.toDate == toDate) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.sortBy, sortBy) || other.sortBy == sortBy) &&
            (identical(other.sortDirection, sortDirection) ||
                other.sortDirection == sortDirection));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    complainNumber,
    customerName,
    customerMobile,
    status,
    fromDate,
    toDate,
    page,
    size,
    sortBy,
    sortDirection,
  );

  /// Create a copy of ComplaintSearchRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ComplaintSearchRequestImplCopyWith<_$ComplaintSearchRequestImpl>
  get copyWith =>
      __$$ComplaintSearchRequestImplCopyWithImpl<_$ComplaintSearchRequestImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ComplaintSearchRequestImplToJson(this);
  }
}

abstract class _ComplaintSearchRequest implements ComplaintSearchRequest {
  const factory _ComplaintSearchRequest({
    final String? complainNumber,
    final String? customerName,
    final String? customerMobile,
    final String? status,
    final DateTime? fromDate,
    final DateTime? toDate,
    final int? page,
    final int? size,
    final String? sortBy,
    final String? sortDirection,
  }) = _$ComplaintSearchRequestImpl;

  factory _ComplaintSearchRequest.fromJson(Map<String, dynamic> json) =
      _$ComplaintSearchRequestImpl.fromJson;

  @override
  String? get complainNumber;
  @override
  String? get customerName;
  @override
  String? get customerMobile;
  @override
  String? get status;
  @override
  DateTime? get fromDate;
  @override
  DateTime? get toDate;
  @override
  int? get page;
  @override
  int? get size;
  @override
  String? get sortBy;
  @override
  String? get sortDirection;

  /// Create a copy of ComplaintSearchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ComplaintSearchRequestImplCopyWith<_$ComplaintSearchRequestImpl>
  get copyWith => throw _privateConstructorUsedError;
}

ComplaintSearchResponse _$ComplaintSearchResponseFromJson(
  Map<String, dynamic> json,
) {
  return _ComplaintSearchResponse.fromJson(json);
}

/// @nodoc
mixin _$ComplaintSearchResponse {
  int get responseCode => throw _privateConstructorUsedError;
  String get responseMessage => throw _privateConstructorUsedError;
  List<ComplaintListItem>? get response => throw _privateConstructorUsedError;
  bool? get success => throw _privateConstructorUsedError;
  int? get totalElements => throw _privateConstructorUsedError;
  int? get totalPages => throw _privateConstructorUsedError;
  int? get currentPage => throw _privateConstructorUsedError;

  /// Serializes this ComplaintSearchResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ComplaintSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ComplaintSearchResponseCopyWith<ComplaintSearchResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ComplaintSearchResponseCopyWith<$Res> {
  factory $ComplaintSearchResponseCopyWith(
    ComplaintSearchResponse value,
    $Res Function(ComplaintSearchResponse) then,
  ) = _$ComplaintSearchResponseCopyWithImpl<$Res, ComplaintSearchResponse>;
  @useResult
  $Res call({
    int responseCode,
    String responseMessage,
    List<ComplaintListItem>? response,
    bool? success,
    int? totalElements,
    int? totalPages,
    int? currentPage,
  });
}

/// @nodoc
class _$ComplaintSearchResponseCopyWithImpl<
  $Res,
  $Val extends ComplaintSearchResponse
>
    implements $ComplaintSearchResponseCopyWith<$Res> {
  _$ComplaintSearchResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ComplaintSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? responseCode = null,
    Object? responseMessage = null,
    Object? response = freezed,
    Object? success = freezed,
    Object? totalElements = freezed,
    Object? totalPages = freezed,
    Object? currentPage = freezed,
  }) {
    return _then(
      _value.copyWith(
            responseCode: null == responseCode
                ? _value.responseCode
                : responseCode // ignore: cast_nullable_to_non_nullable
                      as int,
            responseMessage: null == responseMessage
                ? _value.responseMessage
                : responseMessage // ignore: cast_nullable_to_non_nullable
                      as String,
            response: freezed == response
                ? _value.response
                : response // ignore: cast_nullable_to_non_nullable
                      as List<ComplaintListItem>?,
            success: freezed == success
                ? _value.success
                : success // ignore: cast_nullable_to_non_nullable
                      as bool?,
            totalElements: freezed == totalElements
                ? _value.totalElements
                : totalElements // ignore: cast_nullable_to_non_nullable
                      as int?,
            totalPages: freezed == totalPages
                ? _value.totalPages
                : totalPages // ignore: cast_nullable_to_non_nullable
                      as int?,
            currentPage: freezed == currentPage
                ? _value.currentPage
                : currentPage // ignore: cast_nullable_to_non_nullable
                      as int?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ComplaintSearchResponseImplCopyWith<$Res>
    implements $ComplaintSearchResponseCopyWith<$Res> {
  factory _$$ComplaintSearchResponseImplCopyWith(
    _$ComplaintSearchResponseImpl value,
    $Res Function(_$ComplaintSearchResponseImpl) then,
  ) = __$$ComplaintSearchResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int responseCode,
    String responseMessage,
    List<ComplaintListItem>? response,
    bool? success,
    int? totalElements,
    int? totalPages,
    int? currentPage,
  });
}

/// @nodoc
class __$$ComplaintSearchResponseImplCopyWithImpl<$Res>
    extends
        _$ComplaintSearchResponseCopyWithImpl<
          $Res,
          _$ComplaintSearchResponseImpl
        >
    implements _$$ComplaintSearchResponseImplCopyWith<$Res> {
  __$$ComplaintSearchResponseImplCopyWithImpl(
    _$ComplaintSearchResponseImpl _value,
    $Res Function(_$ComplaintSearchResponseImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ComplaintSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? responseCode = null,
    Object? responseMessage = null,
    Object? response = freezed,
    Object? success = freezed,
    Object? totalElements = freezed,
    Object? totalPages = freezed,
    Object? currentPage = freezed,
  }) {
    return _then(
      _$ComplaintSearchResponseImpl(
        responseCode: null == responseCode
            ? _value.responseCode
            : responseCode // ignore: cast_nullable_to_non_nullable
                  as int,
        responseMessage: null == responseMessage
            ? _value.responseMessage
            : responseMessage // ignore: cast_nullable_to_non_nullable
                  as String,
        response: freezed == response
            ? _value._response
            : response // ignore: cast_nullable_to_non_nullable
                  as List<ComplaintListItem>?,
        success: freezed == success
            ? _value.success
            : success // ignore: cast_nullable_to_non_nullable
                  as bool?,
        totalElements: freezed == totalElements
            ? _value.totalElements
            : totalElements // ignore: cast_nullable_to_non_nullable
                  as int?,
        totalPages: freezed == totalPages
            ? _value.totalPages
            : totalPages // ignore: cast_nullable_to_non_nullable
                  as int?,
        currentPage: freezed == currentPage
            ? _value.currentPage
            : currentPage // ignore: cast_nullable_to_non_nullable
                  as int?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ComplaintSearchResponseImpl implements _ComplaintSearchResponse {
  const _$ComplaintSearchResponseImpl({
    required this.responseCode,
    required this.responseMessage,
    final List<ComplaintListItem>? response,
    this.success,
    this.totalElements,
    this.totalPages,
    this.currentPage,
  }) : _response = response;

  factory _$ComplaintSearchResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ComplaintSearchResponseImplFromJson(json);

  @override
  final int responseCode;
  @override
  final String responseMessage;
  final List<ComplaintListItem>? _response;
  @override
  List<ComplaintListItem>? get response {
    final value = _response;
    if (value == null) return null;
    if (_response is EqualUnmodifiableListView) return _response;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? success;
  @override
  final int? totalElements;
  @override
  final int? totalPages;
  @override
  final int? currentPage;

  @override
  String toString() {
    return 'ComplaintSearchResponse(responseCode: $responseCode, responseMessage: $responseMessage, response: $response, success: $success, totalElements: $totalElements, totalPages: $totalPages, currentPage: $currentPage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ComplaintSearchResponseImpl &&
            (identical(other.responseCode, responseCode) ||
                other.responseCode == responseCode) &&
            (identical(other.responseMessage, responseMessage) ||
                other.responseMessage == responseMessage) &&
            const DeepCollectionEquality().equals(other._response, _response) &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.totalElements, totalElements) ||
                other.totalElements == totalElements) &&
            (identical(other.totalPages, totalPages) ||
                other.totalPages == totalPages) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    responseCode,
    responseMessage,
    const DeepCollectionEquality().hash(_response),
    success,
    totalElements,
    totalPages,
    currentPage,
  );

  /// Create a copy of ComplaintSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ComplaintSearchResponseImplCopyWith<_$ComplaintSearchResponseImpl>
  get copyWith =>
      __$$ComplaintSearchResponseImplCopyWithImpl<
        _$ComplaintSearchResponseImpl
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ComplaintSearchResponseImplToJson(this);
  }
}

abstract class _ComplaintSearchResponse implements ComplaintSearchResponse {
  const factory _ComplaintSearchResponse({
    required final int responseCode,
    required final String responseMessage,
    final List<ComplaintListItem>? response,
    final bool? success,
    final int? totalElements,
    final int? totalPages,
    final int? currentPage,
  }) = _$ComplaintSearchResponseImpl;

  factory _ComplaintSearchResponse.fromJson(Map<String, dynamic> json) =
      _$ComplaintSearchResponseImpl.fromJson;

  @override
  int get responseCode;
  @override
  String get responseMessage;
  @override
  List<ComplaintListItem>? get response;
  @override
  bool? get success;
  @override
  int? get totalElements;
  @override
  int? get totalPages;
  @override
  int? get currentPage;

  /// Create a copy of ComplaintSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ComplaintSearchResponseImplCopyWith<_$ComplaintSearchResponseImpl>
  get copyWith => throw _privateConstructorUsedError;
}

Visit _$VisitFromJson(Map<String, dynamic> json) {
  return _Visit.fromJson(json);
}

/// @nodoc
mixin _$Visit {
  int? get visitId => throw _privateConstructorUsedError;
  int? get complainId => throw _privateConstructorUsedError;
  DateTime? get visitDate => throw _privateConstructorUsedError;
  String? get visitStatus => throw _privateConstructorUsedError;
  int? get amountCollected => throw _privateConstructorUsedError;
  String? get technicianRemark => throw _privateConstructorUsedError;
  bool? get inOut => throw _privateConstructorUsedError;
  bool? get customerMisuse => throw _privateConstructorUsedError;
  Employee? get technician => throw _privateConstructorUsedError;

  /// Serializes this Visit to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Visit
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VisitCopyWith<Visit> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VisitCopyWith<$Res> {
  factory $VisitCopyWith(Visit value, $Res Function(Visit) then) =
      _$VisitCopyWithImpl<$Res, Visit>;
  @useResult
  $Res call({
    int? visitId,
    int? complainId,
    DateTime? visitDate,
    String? visitStatus,
    int? amountCollected,
    String? technicianRemark,
    bool? inOut,
    bool? customerMisuse,
    Employee? technician,
  });

  $EmployeeCopyWith<$Res>? get technician;
}

/// @nodoc
class _$VisitCopyWithImpl<$Res, $Val extends Visit>
    implements $VisitCopyWith<$Res> {
  _$VisitCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Visit
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? visitId = freezed,
    Object? complainId = freezed,
    Object? visitDate = freezed,
    Object? visitStatus = freezed,
    Object? amountCollected = freezed,
    Object? technicianRemark = freezed,
    Object? inOut = freezed,
    Object? customerMisuse = freezed,
    Object? technician = freezed,
  }) {
    return _then(
      _value.copyWith(
            visitId: freezed == visitId
                ? _value.visitId
                : visitId // ignore: cast_nullable_to_non_nullable
                      as int?,
            complainId: freezed == complainId
                ? _value.complainId
                : complainId // ignore: cast_nullable_to_non_nullable
                      as int?,
            visitDate: freezed == visitDate
                ? _value.visitDate
                : visitDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            visitStatus: freezed == visitStatus
                ? _value.visitStatus
                : visitStatus // ignore: cast_nullable_to_non_nullable
                      as String?,
            amountCollected: freezed == amountCollected
                ? _value.amountCollected
                : amountCollected // ignore: cast_nullable_to_non_nullable
                      as int?,
            technicianRemark: freezed == technicianRemark
                ? _value.technicianRemark
                : technicianRemark // ignore: cast_nullable_to_non_nullable
                      as String?,
            inOut: freezed == inOut
                ? _value.inOut
                : inOut // ignore: cast_nullable_to_non_nullable
                      as bool?,
            customerMisuse: freezed == customerMisuse
                ? _value.customerMisuse
                : customerMisuse // ignore: cast_nullable_to_non_nullable
                      as bool?,
            technician: freezed == technician
                ? _value.technician
                : technician // ignore: cast_nullable_to_non_nullable
                      as Employee?,
          )
          as $Val,
    );
  }

  /// Create a copy of Visit
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EmployeeCopyWith<$Res>? get technician {
    if (_value.technician == null) {
      return null;
    }

    return $EmployeeCopyWith<$Res>(_value.technician!, (value) {
      return _then(_value.copyWith(technician: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$VisitImplCopyWith<$Res> implements $VisitCopyWith<$Res> {
  factory _$$VisitImplCopyWith(
    _$VisitImpl value,
    $Res Function(_$VisitImpl) then,
  ) = __$$VisitImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int? visitId,
    int? complainId,
    DateTime? visitDate,
    String? visitStatus,
    int? amountCollected,
    String? technicianRemark,
    bool? inOut,
    bool? customerMisuse,
    Employee? technician,
  });

  @override
  $EmployeeCopyWith<$Res>? get technician;
}

/// @nodoc
class __$$VisitImplCopyWithImpl<$Res>
    extends _$VisitCopyWithImpl<$Res, _$VisitImpl>
    implements _$$VisitImplCopyWith<$Res> {
  __$$VisitImplCopyWithImpl(
    _$VisitImpl _value,
    $Res Function(_$VisitImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Visit
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? visitId = freezed,
    Object? complainId = freezed,
    Object? visitDate = freezed,
    Object? visitStatus = freezed,
    Object? amountCollected = freezed,
    Object? technicianRemark = freezed,
    Object? inOut = freezed,
    Object? customerMisuse = freezed,
    Object? technician = freezed,
  }) {
    return _then(
      _$VisitImpl(
        visitId: freezed == visitId
            ? _value.visitId
            : visitId // ignore: cast_nullable_to_non_nullable
                  as int?,
        complainId: freezed == complainId
            ? _value.complainId
            : complainId // ignore: cast_nullable_to_non_nullable
                  as int?,
        visitDate: freezed == visitDate
            ? _value.visitDate
            : visitDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        visitStatus: freezed == visitStatus
            ? _value.visitStatus
            : visitStatus // ignore: cast_nullable_to_non_nullable
                  as String?,
        amountCollected: freezed == amountCollected
            ? _value.amountCollected
            : amountCollected // ignore: cast_nullable_to_non_nullable
                  as int?,
        technicianRemark: freezed == technicianRemark
            ? _value.technicianRemark
            : technicianRemark // ignore: cast_nullable_to_non_nullable
                  as String?,
        inOut: freezed == inOut
            ? _value.inOut
            : inOut // ignore: cast_nullable_to_non_nullable
                  as bool?,
        customerMisuse: freezed == customerMisuse
            ? _value.customerMisuse
            : customerMisuse // ignore: cast_nullable_to_non_nullable
                  as bool?,
        technician: freezed == technician
            ? _value.technician
            : technician // ignore: cast_nullable_to_non_nullable
                  as Employee?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$VisitImpl implements _Visit {
  const _$VisitImpl({
    this.visitId,
    this.complainId,
    this.visitDate,
    this.visitStatus,
    this.amountCollected,
    this.technicianRemark,
    this.inOut,
    this.customerMisuse,
    this.technician,
  });

  factory _$VisitImpl.fromJson(Map<String, dynamic> json) =>
      _$$VisitImplFromJson(json);

  @override
  final int? visitId;
  @override
  final int? complainId;
  @override
  final DateTime? visitDate;
  @override
  final String? visitStatus;
  @override
  final int? amountCollected;
  @override
  final String? technicianRemark;
  @override
  final bool? inOut;
  @override
  final bool? customerMisuse;
  @override
  final Employee? technician;

  @override
  String toString() {
    return 'Visit(visitId: $visitId, complainId: $complainId, visitDate: $visitDate, visitStatus: $visitStatus, amountCollected: $amountCollected, technicianRemark: $technicianRemark, inOut: $inOut, customerMisuse: $customerMisuse, technician: $technician)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VisitImpl &&
            (identical(other.visitId, visitId) || other.visitId == visitId) &&
            (identical(other.complainId, complainId) ||
                other.complainId == complainId) &&
            (identical(other.visitDate, visitDate) ||
                other.visitDate == visitDate) &&
            (identical(other.visitStatus, visitStatus) ||
                other.visitStatus == visitStatus) &&
            (identical(other.amountCollected, amountCollected) ||
                other.amountCollected == amountCollected) &&
            (identical(other.technicianRemark, technicianRemark) ||
                other.technicianRemark == technicianRemark) &&
            (identical(other.inOut, inOut) || other.inOut == inOut) &&
            (identical(other.customerMisuse, customerMisuse) ||
                other.customerMisuse == customerMisuse) &&
            (identical(other.technician, technician) ||
                other.technician == technician));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    visitId,
    complainId,
    visitDate,
    visitStatus,
    amountCollected,
    technicianRemark,
    inOut,
    customerMisuse,
    technician,
  );

  /// Create a copy of Visit
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VisitImplCopyWith<_$VisitImpl> get copyWith =>
      __$$VisitImplCopyWithImpl<_$VisitImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VisitImplToJson(this);
  }
}

abstract class _Visit implements Visit {
  const factory _Visit({
    final int? visitId,
    final int? complainId,
    final DateTime? visitDate,
    final String? visitStatus,
    final int? amountCollected,
    final String? technicianRemark,
    final bool? inOut,
    final bool? customerMisuse,
    final Employee? technician,
  }) = _$VisitImpl;

  factory _Visit.fromJson(Map<String, dynamic> json) = _$VisitImpl.fromJson;

  @override
  int? get visitId;
  @override
  int? get complainId;
  @override
  DateTime? get visitDate;
  @override
  String? get visitStatus;
  @override
  int? get amountCollected;
  @override
  String? get technicianRemark;
  @override
  bool? get inOut;
  @override
  bool? get customerMisuse;
  @override
  Employee? get technician;

  /// Create a copy of Visit
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VisitImplCopyWith<_$VisitImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ComplaintTransferRequest _$ComplaintTransferRequestFromJson(
  Map<String, dynamic> json,
) {
  return _ComplaintTransferRequest.fromJson(json);
}

/// @nodoc
mixin _$ComplaintTransferRequest {
  String get visitStatus => throw _privateConstructorUsedError;
  int get amountCollected => throw _privateConstructorUsedError;
  bool? get inOut => throw _privateConstructorUsedError;
  bool? get customerMisuse => throw _privateConstructorUsedError;
  String get technicianRemark => throw _privateConstructorUsedError;
  DateTime get visitDate => throw _privateConstructorUsedError;

  /// Serializes this ComplaintTransferRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ComplaintTransferRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ComplaintTransferRequestCopyWith<ComplaintTransferRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ComplaintTransferRequestCopyWith<$Res> {
  factory $ComplaintTransferRequestCopyWith(
    ComplaintTransferRequest value,
    $Res Function(ComplaintTransferRequest) then,
  ) = _$ComplaintTransferRequestCopyWithImpl<$Res, ComplaintTransferRequest>;
  @useResult
  $Res call({
    String visitStatus,
    int amountCollected,
    bool? inOut,
    bool? customerMisuse,
    String technicianRemark,
    DateTime visitDate,
  });
}

/// @nodoc
class _$ComplaintTransferRequestCopyWithImpl<
  $Res,
  $Val extends ComplaintTransferRequest
>
    implements $ComplaintTransferRequestCopyWith<$Res> {
  _$ComplaintTransferRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ComplaintTransferRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? visitStatus = null,
    Object? amountCollected = null,
    Object? inOut = freezed,
    Object? customerMisuse = freezed,
    Object? technicianRemark = null,
    Object? visitDate = null,
  }) {
    return _then(
      _value.copyWith(
            visitStatus: null == visitStatus
                ? _value.visitStatus
                : visitStatus // ignore: cast_nullable_to_non_nullable
                      as String,
            amountCollected: null == amountCollected
                ? _value.amountCollected
                : amountCollected // ignore: cast_nullable_to_non_nullable
                      as int,
            inOut: freezed == inOut
                ? _value.inOut
                : inOut // ignore: cast_nullable_to_non_nullable
                      as bool?,
            customerMisuse: freezed == customerMisuse
                ? _value.customerMisuse
                : customerMisuse // ignore: cast_nullable_to_non_nullable
                      as bool?,
            technicianRemark: null == technicianRemark
                ? _value.technicianRemark
                : technicianRemark // ignore: cast_nullable_to_non_nullable
                      as String,
            visitDate: null == visitDate
                ? _value.visitDate
                : visitDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ComplaintTransferRequestImplCopyWith<$Res>
    implements $ComplaintTransferRequestCopyWith<$Res> {
  factory _$$ComplaintTransferRequestImplCopyWith(
    _$ComplaintTransferRequestImpl value,
    $Res Function(_$ComplaintTransferRequestImpl) then,
  ) = __$$ComplaintTransferRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String visitStatus,
    int amountCollected,
    bool? inOut,
    bool? customerMisuse,
    String technicianRemark,
    DateTime visitDate,
  });
}

/// @nodoc
class __$$ComplaintTransferRequestImplCopyWithImpl<$Res>
    extends
        _$ComplaintTransferRequestCopyWithImpl<
          $Res,
          _$ComplaintTransferRequestImpl
        >
    implements _$$ComplaintTransferRequestImplCopyWith<$Res> {
  __$$ComplaintTransferRequestImplCopyWithImpl(
    _$ComplaintTransferRequestImpl _value,
    $Res Function(_$ComplaintTransferRequestImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ComplaintTransferRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? visitStatus = null,
    Object? amountCollected = null,
    Object? inOut = freezed,
    Object? customerMisuse = freezed,
    Object? technicianRemark = null,
    Object? visitDate = null,
  }) {
    return _then(
      _$ComplaintTransferRequestImpl(
        visitStatus: null == visitStatus
            ? _value.visitStatus
            : visitStatus // ignore: cast_nullable_to_non_nullable
                  as String,
        amountCollected: null == amountCollected
            ? _value.amountCollected
            : amountCollected // ignore: cast_nullable_to_non_nullable
                  as int,
        inOut: freezed == inOut
            ? _value.inOut
            : inOut // ignore: cast_nullable_to_non_nullable
                  as bool?,
        customerMisuse: freezed == customerMisuse
            ? _value.customerMisuse
            : customerMisuse // ignore: cast_nullable_to_non_nullable
                  as bool?,
        technicianRemark: null == technicianRemark
            ? _value.technicianRemark
            : technicianRemark // ignore: cast_nullable_to_non_nullable
                  as String,
        visitDate: null == visitDate
            ? _value.visitDate
            : visitDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ComplaintTransferRequestImpl implements _ComplaintTransferRequest {
  const _$ComplaintTransferRequestImpl({
    required this.visitStatus,
    required this.amountCollected,
    this.inOut,
    this.customerMisuse,
    required this.technicianRemark,
    required this.visitDate,
  });

  factory _$ComplaintTransferRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$ComplaintTransferRequestImplFromJson(json);

  @override
  final String visitStatus;
  @override
  final int amountCollected;
  @override
  final bool? inOut;
  @override
  final bool? customerMisuse;
  @override
  final String technicianRemark;
  @override
  final DateTime visitDate;

  @override
  String toString() {
    return 'ComplaintTransferRequest(visitStatus: $visitStatus, amountCollected: $amountCollected, inOut: $inOut, customerMisuse: $customerMisuse, technicianRemark: $technicianRemark, visitDate: $visitDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ComplaintTransferRequestImpl &&
            (identical(other.visitStatus, visitStatus) ||
                other.visitStatus == visitStatus) &&
            (identical(other.amountCollected, amountCollected) ||
                other.amountCollected == amountCollected) &&
            (identical(other.inOut, inOut) || other.inOut == inOut) &&
            (identical(other.customerMisuse, customerMisuse) ||
                other.customerMisuse == customerMisuse) &&
            (identical(other.technicianRemark, technicianRemark) ||
                other.technicianRemark == technicianRemark) &&
            (identical(other.visitDate, visitDate) ||
                other.visitDate == visitDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    visitStatus,
    amountCollected,
    inOut,
    customerMisuse,
    technicianRemark,
    visitDate,
  );

  /// Create a copy of ComplaintTransferRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ComplaintTransferRequestImplCopyWith<_$ComplaintTransferRequestImpl>
  get copyWith =>
      __$$ComplaintTransferRequestImplCopyWithImpl<
        _$ComplaintTransferRequestImpl
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ComplaintTransferRequestImplToJson(this);
  }
}

abstract class _ComplaintTransferRequest implements ComplaintTransferRequest {
  const factory _ComplaintTransferRequest({
    required final String visitStatus,
    required final int amountCollected,
    final bool? inOut,
    final bool? customerMisuse,
    required final String technicianRemark,
    required final DateTime visitDate,
  }) = _$ComplaintTransferRequestImpl;

  factory _ComplaintTransferRequest.fromJson(Map<String, dynamic> json) =
      _$ComplaintTransferRequestImpl.fromJson;

  @override
  String get visitStatus;
  @override
  int get amountCollected;
  @override
  bool? get inOut;
  @override
  bool? get customerMisuse;
  @override
  String get technicianRemark;
  @override
  DateTime get visitDate;

  /// Create a copy of ComplaintTransferRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ComplaintTransferRequestImplCopyWith<_$ComplaintTransferRequestImpl>
  get copyWith => throw _privateConstructorUsedError;
}

CloseComplaintRequest _$CloseComplaintRequestFromJson(
  Map<String, dynamic> json,
) {
  return _CloseComplaintRequest.fromJson(json);
}

/// @nodoc
mixin _$CloseComplaintRequest {
  String get closingRemark => throw _privateConstructorUsedError;
  DateTime get closeDate => throw _privateConstructorUsedError;

  /// Serializes this CloseComplaintRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CloseComplaintRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CloseComplaintRequestCopyWith<CloseComplaintRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CloseComplaintRequestCopyWith<$Res> {
  factory $CloseComplaintRequestCopyWith(
    CloseComplaintRequest value,
    $Res Function(CloseComplaintRequest) then,
  ) = _$CloseComplaintRequestCopyWithImpl<$Res, CloseComplaintRequest>;
  @useResult
  $Res call({String closingRemark, DateTime closeDate});
}

/// @nodoc
class _$CloseComplaintRequestCopyWithImpl<
  $Res,
  $Val extends CloseComplaintRequest
>
    implements $CloseComplaintRequestCopyWith<$Res> {
  _$CloseComplaintRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CloseComplaintRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? closingRemark = null, Object? closeDate = null}) {
    return _then(
      _value.copyWith(
            closingRemark: null == closingRemark
                ? _value.closingRemark
                : closingRemark // ignore: cast_nullable_to_non_nullable
                      as String,
            closeDate: null == closeDate
                ? _value.closeDate
                : closeDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CloseComplaintRequestImplCopyWith<$Res>
    implements $CloseComplaintRequestCopyWith<$Res> {
  factory _$$CloseComplaintRequestImplCopyWith(
    _$CloseComplaintRequestImpl value,
    $Res Function(_$CloseComplaintRequestImpl) then,
  ) = __$$CloseComplaintRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String closingRemark, DateTime closeDate});
}

/// @nodoc
class __$$CloseComplaintRequestImplCopyWithImpl<$Res>
    extends
        _$CloseComplaintRequestCopyWithImpl<$Res, _$CloseComplaintRequestImpl>
    implements _$$CloseComplaintRequestImplCopyWith<$Res> {
  __$$CloseComplaintRequestImplCopyWithImpl(
    _$CloseComplaintRequestImpl _value,
    $Res Function(_$CloseComplaintRequestImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CloseComplaintRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? closingRemark = null, Object? closeDate = null}) {
    return _then(
      _$CloseComplaintRequestImpl(
        closingRemark: null == closingRemark
            ? _value.closingRemark
            : closingRemark // ignore: cast_nullable_to_non_nullable
                  as String,
        closeDate: null == closeDate
            ? _value.closeDate
            : closeDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CloseComplaintRequestImpl implements _CloseComplaintRequest {
  const _$CloseComplaintRequestImpl({
    required this.closingRemark,
    required this.closeDate,
  });

  factory _$CloseComplaintRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$CloseComplaintRequestImplFromJson(json);

  @override
  final String closingRemark;
  @override
  final DateTime closeDate;

  @override
  String toString() {
    return 'CloseComplaintRequest(closingRemark: $closingRemark, closeDate: $closeDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CloseComplaintRequestImpl &&
            (identical(other.closingRemark, closingRemark) ||
                other.closingRemark == closingRemark) &&
            (identical(other.closeDate, closeDate) ||
                other.closeDate == closeDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, closingRemark, closeDate);

  /// Create a copy of CloseComplaintRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CloseComplaintRequestImplCopyWith<_$CloseComplaintRequestImpl>
  get copyWith =>
      __$$CloseComplaintRequestImplCopyWithImpl<_$CloseComplaintRequestImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CloseComplaintRequestImplToJson(this);
  }
}

abstract class _CloseComplaintRequest implements CloseComplaintRequest {
  const factory _CloseComplaintRequest({
    required final String closingRemark,
    required final DateTime closeDate,
  }) = _$CloseComplaintRequestImpl;

  factory _CloseComplaintRequest.fromJson(Map<String, dynamic> json) =
      _$CloseComplaintRequestImpl.fromJson;

  @override
  String get closingRemark;
  @override
  DateTime get closeDate;

  /// Create a copy of CloseComplaintRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CloseComplaintRequestImplCopyWith<_$CloseComplaintRequestImpl>
  get copyWith => throw _privateConstructorUsedError;
}
