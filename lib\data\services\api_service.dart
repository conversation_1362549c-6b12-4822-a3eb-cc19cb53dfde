import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user.dart';
import '../models/complaint.dart';
import '../models/customer.dart';
import '../models/employee.dart';
import '../models/product.dart';
import 'storage_service.dart';
import '../../core/constants/app_constants.dart';

class ApiService {
  late final Dio _dio;

  ApiService() {
    _dio = Dio();
    _dio.options.baseUrl = AppConstants.baseUrl;
    _dio.options.connectTimeout = const Duration(
      milliseconds: AppConstants.apiTimeout,
    );
    _dio.options.receiveTimeout = const Duration(
      milliseconds: AppConstants.apiTimeout,
    );
    _dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'ComplaintManagement-Flutter/1.0',
    };

    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Add JWT token to requests (except auth endpoints)
          final token = await StorageService.getToken();
          final isAuthEndpoint = options.path.contains('auth-service/auth');

          if (token != null && !isAuthEndpoint) {
            options.headers['Authorization'] = 'Bearer $token';
          }

          // Add office code header if available
          final officeCode = await StorageService.getOfficeCode();
          if (officeCode != null) {
            options.headers['Office-Code'] = officeCode;
          }

          handler.next(options);
        },
        onError: (error, handler) async {
          if (error.response?.statusCode == 401) {
            // Handle token expiration - clear storage
            await StorageService.clearAll();

            // Note: Navigation should be handled by the UI layer
            // The auth provider will detect the cleared storage and update state
            print('Token expired - cleared storage');
          }
          handler.next(error);
        },
      ),
    );
  }

  // Authentication (matching working curl request)
  Future<Map<String, dynamic>> login(AuthRequest request) async {
    try {
      final response = await _dio.post(
        '${AppConstants.authServicePath}/authenticate',
        data: request.toJson(),
        options: Options(
          headers: {'accept': '*/*', 'Content-Type': 'application/json'},
        ),
      );
      return response.data as Map<String, dynamic>;
    } catch (e) {
      throw _handleError(e);
    }
  }

  // Complaints
  Future<ComplaintSearchResponse> searchComplaints(
    ComplaintSearchRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '${AppConstants.complainServicePath}/user/complain/search',
        data: request.toJson(),
      );
      return ComplaintSearchResponse.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<StandardCreateResponse> createComplaint(
    ComplaintCreateRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '${AppConstants.complainServicePath}/user/complain/create',
        data: request.toJson(),
      );
      return StandardCreateResponse.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<StandardCreateResponse> createComplaintForExistingCustomer(
    int customerId,
    ProductCreateRequest productRequest,
    ComplaintRegisterRequest complainRequest,
  ) async {
    try {
      final response = await _dio.post(
        '${AppConstants.complainServicePath}/user/complain/existing/create/$customerId',
        data: {...productRequest.toJson(), ...complainRequest.toJson()},
      );
      return StandardCreateResponse.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<StandardCreateResponse> createComplaintForExistingCustomerAndProduct(
    int customerId,
    int productId,
    ComplaintRegisterRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '${AppConstants.complainServicePath}/user/complain/existing/create/$customerId/$productId',
        data: request.toJson(),
      );
      return StandardCreateResponse.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<GenericResponse<Complaint>> getComplaintById(int complainId) async {
    try {
      final response = await _dio.get(
        '${AppConstants.complainServicePath}/user/complain/view/$complainId',
      );
      return GenericResponse.fromJson(
        response.data,
        (json) => Complaint.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<StandardCreateResponse> assignComplaint(
    int complainId,
    int employeeId,
  ) async {
    try {
      final response = await _dio.post(
        '${AppConstants.complainServicePath}/user/complain/assign/$complainId/$employeeId',
      );
      return StandardCreateResponse.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<StandardCreateResponse> createVisit(
    int complainId,
    ComplaintTransferRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '${AppConstants.complainServicePath}/user/complain/visit/$complainId',
        data: request.toJson(),
      );
      return StandardCreateResponse.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<StandardCreateResponse> closeComplaint(
    int complainId,
    CloseComplaintRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '${AppConstants.complainServicePath}/user/complain/close/$complainId',
        data: request.toJson(),
      );
      return StandardCreateResponse.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  // Customers
  Future<CustomerSearchResponse> searchCustomers(
    Map<String, dynamic> searchParams,
  ) async {
    try {
      final response = await _dio.post(
        '${AppConstants.complainServicePath}/user/cust/search',
        data: searchParams,
      );
      return CustomerSearchResponse.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<StandardCreateResponse> createCustomer(
    CustomerCreateRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '${AppConstants.complainServicePath}/user/cust/create',
        data: request.toJson(),
      );
      return StandardCreateResponse.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<GenericResponse<CustomerListItem>> getCustomerById(
    int customerId,
  ) async {
    try {
      final response = await _dio.get(
        '${AppConstants.complainServicePath}/user/cust/search/$customerId',
      );
      return GenericResponse.fromJson(
        response.data,
        (json) => CustomerListItem.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      throw _handleError(e);
    }
  }

  // Employees
  Future<EmployeeSearchResponse> searchEmployees(
    Map<String, dynamic> searchParams,
  ) async {
    try {
      final response = await _dio.post(
        '${AppConstants.complainServicePath}/user/emp/search',
        data: searchParams,
      );
      return EmployeeSearchResponse.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<StandardCreateResponse> createEmployee(
    EmployeeCreateRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '${AppConstants.complainServicePath}/user/emp/create',
        data: request.toJson(),
      );
      return StandardCreateResponse.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  // Products
  Future<ProductSearchResponse> searchProducts(
    Map<String, dynamic> searchParams,
  ) async {
    try {
      final response = await _dio.post(
        '${AppConstants.complainServicePath}/user/product/search',
        data: searchParams,
      );
      return ProductSearchResponse.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<StandardCreateResponse> createProduct(
    ProductCreateRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '${AppConstants.complainServicePath}/user/product/create',
        data: request.toJson(),
      );
      return StandardCreateResponse.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  String _handleError(dynamic error) {
    if (error is DioException) {
      // Handle server response errors
      if (error.response?.data != null) {
        final responseData = error.response!.data;
        if (responseData is Map<String, dynamic>) {
          // Check for server-formatted error response
          if (responseData.containsKey('responseMessage')) {
            return responseData['responseMessage'] as String;
          }
          if (responseData.containsKey('message')) {
            return responseData['message'] as String;
          }
        }
      }

      // Handle HTTP status codes
      switch (error.response?.statusCode) {
        case 401:
          return 'Unauthorized: Invalid token or credentials';
        case 403:
          return 'Access forbidden';
        case 404:
          return 'Service not found';
        case 500:
          return 'Internal server error';
        case 502:
          return 'Bad gateway - server is down';
        case 503:
          return 'Service unavailable';
        default:
          if (error.type == DioExceptionType.connectionTimeout) {
            return 'Connection timeout. Please check your internet connection.';
          } else if (error.type == DioExceptionType.receiveTimeout) {
            return 'Request timeout. Server is taking too long to respond.';
          } else if (error.type == DioExceptionType.connectionError) {
            return 'Connection error. Please check your internet connection.';
          }
          return error.message ?? 'Network error occurred';
      }
    }
    return 'Something went wrong. Please try again.';
  }
}

// Provider for ApiService
final apiServiceProvider = Provider<ApiService>((ref) => ApiService());
