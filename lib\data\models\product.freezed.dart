// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

Product _$ProductFromJson(Map<String, dynamic> json) {
  return _Product.fromJson(json);
}

/// @nodoc
mixin _$Product {
  int? get productId => throw _privateConstructorUsedError;
  String get productName => throw _privateConstructorUsedError;
  String get brand => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get modelNumber => throw _privateConstructorUsedError;
  String? get serialNumber => throw _privateConstructorUsedError;
  DateTime? get dateOfPurchase => throw _privateConstructorUsedError;
  int? get customerId => throw _privateConstructorUsedError;

  /// Serializes this Product to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Product
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductCopyWith<Product> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductCopyWith<$Res> {
  factory $ProductCopyWith(Product value, $Res Function(Product) then) =
      _$ProductCopyWithImpl<$Res, Product>;
  @useResult
  $Res call({
    int? productId,
    String productName,
    String brand,
    String? description,
    String? modelNumber,
    String? serialNumber,
    DateTime? dateOfPurchase,
    int? customerId,
  });
}

/// @nodoc
class _$ProductCopyWithImpl<$Res, $Val extends Product>
    implements $ProductCopyWith<$Res> {
  _$ProductCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Product
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? productName = null,
    Object? brand = null,
    Object? description = freezed,
    Object? modelNumber = freezed,
    Object? serialNumber = freezed,
    Object? dateOfPurchase = freezed,
    Object? customerId = freezed,
  }) {
    return _then(
      _value.copyWith(
            productId: freezed == productId
                ? _value.productId
                : productId // ignore: cast_nullable_to_non_nullable
                      as int?,
            productName: null == productName
                ? _value.productName
                : productName // ignore: cast_nullable_to_non_nullable
                      as String,
            brand: null == brand
                ? _value.brand
                : brand // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            modelNumber: freezed == modelNumber
                ? _value.modelNumber
                : modelNumber // ignore: cast_nullable_to_non_nullable
                      as String?,
            serialNumber: freezed == serialNumber
                ? _value.serialNumber
                : serialNumber // ignore: cast_nullable_to_non_nullable
                      as String?,
            dateOfPurchase: freezed == dateOfPurchase
                ? _value.dateOfPurchase
                : dateOfPurchase // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            customerId: freezed == customerId
                ? _value.customerId
                : customerId // ignore: cast_nullable_to_non_nullable
                      as int?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ProductImplCopyWith<$Res> implements $ProductCopyWith<$Res> {
  factory _$$ProductImplCopyWith(
    _$ProductImpl value,
    $Res Function(_$ProductImpl) then,
  ) = __$$ProductImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int? productId,
    String productName,
    String brand,
    String? description,
    String? modelNumber,
    String? serialNumber,
    DateTime? dateOfPurchase,
    int? customerId,
  });
}

/// @nodoc
class __$$ProductImplCopyWithImpl<$Res>
    extends _$ProductCopyWithImpl<$Res, _$ProductImpl>
    implements _$$ProductImplCopyWith<$Res> {
  __$$ProductImplCopyWithImpl(
    _$ProductImpl _value,
    $Res Function(_$ProductImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Product
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? productName = null,
    Object? brand = null,
    Object? description = freezed,
    Object? modelNumber = freezed,
    Object? serialNumber = freezed,
    Object? dateOfPurchase = freezed,
    Object? customerId = freezed,
  }) {
    return _then(
      _$ProductImpl(
        productId: freezed == productId
            ? _value.productId
            : productId // ignore: cast_nullable_to_non_nullable
                  as int?,
        productName: null == productName
            ? _value.productName
            : productName // ignore: cast_nullable_to_non_nullable
                  as String,
        brand: null == brand
            ? _value.brand
            : brand // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        modelNumber: freezed == modelNumber
            ? _value.modelNumber
            : modelNumber // ignore: cast_nullable_to_non_nullable
                  as String?,
        serialNumber: freezed == serialNumber
            ? _value.serialNumber
            : serialNumber // ignore: cast_nullable_to_non_nullable
                  as String?,
        dateOfPurchase: freezed == dateOfPurchase
            ? _value.dateOfPurchase
            : dateOfPurchase // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        customerId: freezed == customerId
            ? _value.customerId
            : customerId // ignore: cast_nullable_to_non_nullable
                  as int?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductImpl implements _Product {
  const _$ProductImpl({
    this.productId,
    required this.productName,
    required this.brand,
    this.description,
    this.modelNumber,
    this.serialNumber,
    this.dateOfPurchase,
    this.customerId,
  });

  factory _$ProductImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductImplFromJson(json);

  @override
  final int? productId;
  @override
  final String productName;
  @override
  final String brand;
  @override
  final String? description;
  @override
  final String? modelNumber;
  @override
  final String? serialNumber;
  @override
  final DateTime? dateOfPurchase;
  @override
  final int? customerId;

  @override
  String toString() {
    return 'Product(productId: $productId, productName: $productName, brand: $brand, description: $description, modelNumber: $modelNumber, serialNumber: $serialNumber, dateOfPurchase: $dateOfPurchase, customerId: $customerId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductImpl &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.brand, brand) || other.brand == brand) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.modelNumber, modelNumber) ||
                other.modelNumber == modelNumber) &&
            (identical(other.serialNumber, serialNumber) ||
                other.serialNumber == serialNumber) &&
            (identical(other.dateOfPurchase, dateOfPurchase) ||
                other.dateOfPurchase == dateOfPurchase) &&
            (identical(other.customerId, customerId) ||
                other.customerId == customerId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    productId,
    productName,
    brand,
    description,
    modelNumber,
    serialNumber,
    dateOfPurchase,
    customerId,
  );

  /// Create a copy of Product
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductImplCopyWith<_$ProductImpl> get copyWith =>
      __$$ProductImplCopyWithImpl<_$ProductImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductImplToJson(this);
  }
}

abstract class _Product implements Product {
  const factory _Product({
    final int? productId,
    required final String productName,
    required final String brand,
    final String? description,
    final String? modelNumber,
    final String? serialNumber,
    final DateTime? dateOfPurchase,
    final int? customerId,
  }) = _$ProductImpl;

  factory _Product.fromJson(Map<String, dynamic> json) = _$ProductImpl.fromJson;

  @override
  int? get productId;
  @override
  String get productName;
  @override
  String get brand;
  @override
  String? get description;
  @override
  String? get modelNumber;
  @override
  String? get serialNumber;
  @override
  DateTime? get dateOfPurchase;
  @override
  int? get customerId;

  /// Create a copy of Product
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductImplCopyWith<_$ProductImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductCreateRequest _$ProductCreateRequestFromJson(Map<String, dynamic> json) {
  return _ProductCreateRequest.fromJson(json);
}

/// @nodoc
mixin _$ProductCreateRequest {
  String get productName => throw _privateConstructorUsedError;
  String get brand => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get modelNumber => throw _privateConstructorUsedError;
  String? get serialNumber => throw _privateConstructorUsedError;
  DateTime? get dateOfPurchase => throw _privateConstructorUsedError;

  /// Serializes this ProductCreateRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductCreateRequestCopyWith<ProductCreateRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductCreateRequestCopyWith<$Res> {
  factory $ProductCreateRequestCopyWith(
    ProductCreateRequest value,
    $Res Function(ProductCreateRequest) then,
  ) = _$ProductCreateRequestCopyWithImpl<$Res, ProductCreateRequest>;
  @useResult
  $Res call({
    String productName,
    String brand,
    String? description,
    String? modelNumber,
    String? serialNumber,
    DateTime? dateOfPurchase,
  });
}

/// @nodoc
class _$ProductCreateRequestCopyWithImpl<
  $Res,
  $Val extends ProductCreateRequest
>
    implements $ProductCreateRequestCopyWith<$Res> {
  _$ProductCreateRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productName = null,
    Object? brand = null,
    Object? description = freezed,
    Object? modelNumber = freezed,
    Object? serialNumber = freezed,
    Object? dateOfPurchase = freezed,
  }) {
    return _then(
      _value.copyWith(
            productName: null == productName
                ? _value.productName
                : productName // ignore: cast_nullable_to_non_nullable
                      as String,
            brand: null == brand
                ? _value.brand
                : brand // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            modelNumber: freezed == modelNumber
                ? _value.modelNumber
                : modelNumber // ignore: cast_nullable_to_non_nullable
                      as String?,
            serialNumber: freezed == serialNumber
                ? _value.serialNumber
                : serialNumber // ignore: cast_nullable_to_non_nullable
                      as String?,
            dateOfPurchase: freezed == dateOfPurchase
                ? _value.dateOfPurchase
                : dateOfPurchase // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ProductCreateRequestImplCopyWith<$Res>
    implements $ProductCreateRequestCopyWith<$Res> {
  factory _$$ProductCreateRequestImplCopyWith(
    _$ProductCreateRequestImpl value,
    $Res Function(_$ProductCreateRequestImpl) then,
  ) = __$$ProductCreateRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String productName,
    String brand,
    String? description,
    String? modelNumber,
    String? serialNumber,
    DateTime? dateOfPurchase,
  });
}

/// @nodoc
class __$$ProductCreateRequestImplCopyWithImpl<$Res>
    extends _$ProductCreateRequestCopyWithImpl<$Res, _$ProductCreateRequestImpl>
    implements _$$ProductCreateRequestImplCopyWith<$Res> {
  __$$ProductCreateRequestImplCopyWithImpl(
    _$ProductCreateRequestImpl _value,
    $Res Function(_$ProductCreateRequestImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProductCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productName = null,
    Object? brand = null,
    Object? description = freezed,
    Object? modelNumber = freezed,
    Object? serialNumber = freezed,
    Object? dateOfPurchase = freezed,
  }) {
    return _then(
      _$ProductCreateRequestImpl(
        productName: null == productName
            ? _value.productName
            : productName // ignore: cast_nullable_to_non_nullable
                  as String,
        brand: null == brand
            ? _value.brand
            : brand // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        modelNumber: freezed == modelNumber
            ? _value.modelNumber
            : modelNumber // ignore: cast_nullable_to_non_nullable
                  as String?,
        serialNumber: freezed == serialNumber
            ? _value.serialNumber
            : serialNumber // ignore: cast_nullable_to_non_nullable
                  as String?,
        dateOfPurchase: freezed == dateOfPurchase
            ? _value.dateOfPurchase
            : dateOfPurchase // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductCreateRequestImpl implements _ProductCreateRequest {
  const _$ProductCreateRequestImpl({
    required this.productName,
    required this.brand,
    this.description,
    this.modelNumber,
    this.serialNumber,
    this.dateOfPurchase,
  });

  factory _$ProductCreateRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductCreateRequestImplFromJson(json);

  @override
  final String productName;
  @override
  final String brand;
  @override
  final String? description;
  @override
  final String? modelNumber;
  @override
  final String? serialNumber;
  @override
  final DateTime? dateOfPurchase;

  @override
  String toString() {
    return 'ProductCreateRequest(productName: $productName, brand: $brand, description: $description, modelNumber: $modelNumber, serialNumber: $serialNumber, dateOfPurchase: $dateOfPurchase)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductCreateRequestImpl &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.brand, brand) || other.brand == brand) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.modelNumber, modelNumber) ||
                other.modelNumber == modelNumber) &&
            (identical(other.serialNumber, serialNumber) ||
                other.serialNumber == serialNumber) &&
            (identical(other.dateOfPurchase, dateOfPurchase) ||
                other.dateOfPurchase == dateOfPurchase));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    productName,
    brand,
    description,
    modelNumber,
    serialNumber,
    dateOfPurchase,
  );

  /// Create a copy of ProductCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductCreateRequestImplCopyWith<_$ProductCreateRequestImpl>
  get copyWith =>
      __$$ProductCreateRequestImplCopyWithImpl<_$ProductCreateRequestImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductCreateRequestImplToJson(this);
  }
}

abstract class _ProductCreateRequest implements ProductCreateRequest {
  const factory _ProductCreateRequest({
    required final String productName,
    required final String brand,
    final String? description,
    final String? modelNumber,
    final String? serialNumber,
    final DateTime? dateOfPurchase,
  }) = _$ProductCreateRequestImpl;

  factory _ProductCreateRequest.fromJson(Map<String, dynamic> json) =
      _$ProductCreateRequestImpl.fromJson;

  @override
  String get productName;
  @override
  String get brand;
  @override
  String? get description;
  @override
  String? get modelNumber;
  @override
  String? get serialNumber;
  @override
  DateTime? get dateOfPurchase;

  /// Create a copy of ProductCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductCreateRequestImplCopyWith<_$ProductCreateRequestImpl>
  get copyWith => throw _privateConstructorUsedError;
}

ProductListItem _$ProductListItemFromJson(Map<String, dynamic> json) {
  return _ProductListItem.fromJson(json);
}

/// @nodoc
mixin _$ProductListItem {
  int? get productId => throw _privateConstructorUsedError;
  String get productName => throw _privateConstructorUsedError;
  String get brand => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get modelNumber => throw _privateConstructorUsedError;
  String? get serialNumber => throw _privateConstructorUsedError;
  DateTime? get dateOfPurchase => throw _privateConstructorUsedError;
  int? get customerId => throw _privateConstructorUsedError;
  String? get customerName => throw _privateConstructorUsedError;
  int? get complainCount => throw _privateConstructorUsedError;

  /// Serializes this ProductListItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductListItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductListItemCopyWith<ProductListItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductListItemCopyWith<$Res> {
  factory $ProductListItemCopyWith(
    ProductListItem value,
    $Res Function(ProductListItem) then,
  ) = _$ProductListItemCopyWithImpl<$Res, ProductListItem>;
  @useResult
  $Res call({
    int? productId,
    String productName,
    String brand,
    String? description,
    String? modelNumber,
    String? serialNumber,
    DateTime? dateOfPurchase,
    int? customerId,
    String? customerName,
    int? complainCount,
  });
}

/// @nodoc
class _$ProductListItemCopyWithImpl<$Res, $Val extends ProductListItem>
    implements $ProductListItemCopyWith<$Res> {
  _$ProductListItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductListItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? productName = null,
    Object? brand = null,
    Object? description = freezed,
    Object? modelNumber = freezed,
    Object? serialNumber = freezed,
    Object? dateOfPurchase = freezed,
    Object? customerId = freezed,
    Object? customerName = freezed,
    Object? complainCount = freezed,
  }) {
    return _then(
      _value.copyWith(
            productId: freezed == productId
                ? _value.productId
                : productId // ignore: cast_nullable_to_non_nullable
                      as int?,
            productName: null == productName
                ? _value.productName
                : productName // ignore: cast_nullable_to_non_nullable
                      as String,
            brand: null == brand
                ? _value.brand
                : brand // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            modelNumber: freezed == modelNumber
                ? _value.modelNumber
                : modelNumber // ignore: cast_nullable_to_non_nullable
                      as String?,
            serialNumber: freezed == serialNumber
                ? _value.serialNumber
                : serialNumber // ignore: cast_nullable_to_non_nullable
                      as String?,
            dateOfPurchase: freezed == dateOfPurchase
                ? _value.dateOfPurchase
                : dateOfPurchase // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            customerId: freezed == customerId
                ? _value.customerId
                : customerId // ignore: cast_nullable_to_non_nullable
                      as int?,
            customerName: freezed == customerName
                ? _value.customerName
                : customerName // ignore: cast_nullable_to_non_nullable
                      as String?,
            complainCount: freezed == complainCount
                ? _value.complainCount
                : complainCount // ignore: cast_nullable_to_non_nullable
                      as int?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ProductListItemImplCopyWith<$Res>
    implements $ProductListItemCopyWith<$Res> {
  factory _$$ProductListItemImplCopyWith(
    _$ProductListItemImpl value,
    $Res Function(_$ProductListItemImpl) then,
  ) = __$$ProductListItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int? productId,
    String productName,
    String brand,
    String? description,
    String? modelNumber,
    String? serialNumber,
    DateTime? dateOfPurchase,
    int? customerId,
    String? customerName,
    int? complainCount,
  });
}

/// @nodoc
class __$$ProductListItemImplCopyWithImpl<$Res>
    extends _$ProductListItemCopyWithImpl<$Res, _$ProductListItemImpl>
    implements _$$ProductListItemImplCopyWith<$Res> {
  __$$ProductListItemImplCopyWithImpl(
    _$ProductListItemImpl _value,
    $Res Function(_$ProductListItemImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProductListItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? productName = null,
    Object? brand = null,
    Object? description = freezed,
    Object? modelNumber = freezed,
    Object? serialNumber = freezed,
    Object? dateOfPurchase = freezed,
    Object? customerId = freezed,
    Object? customerName = freezed,
    Object? complainCount = freezed,
  }) {
    return _then(
      _$ProductListItemImpl(
        productId: freezed == productId
            ? _value.productId
            : productId // ignore: cast_nullable_to_non_nullable
                  as int?,
        productName: null == productName
            ? _value.productName
            : productName // ignore: cast_nullable_to_non_nullable
                  as String,
        brand: null == brand
            ? _value.brand
            : brand // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        modelNumber: freezed == modelNumber
            ? _value.modelNumber
            : modelNumber // ignore: cast_nullable_to_non_nullable
                  as String?,
        serialNumber: freezed == serialNumber
            ? _value.serialNumber
            : serialNumber // ignore: cast_nullable_to_non_nullable
                  as String?,
        dateOfPurchase: freezed == dateOfPurchase
            ? _value.dateOfPurchase
            : dateOfPurchase // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        customerId: freezed == customerId
            ? _value.customerId
            : customerId // ignore: cast_nullable_to_non_nullable
                  as int?,
        customerName: freezed == customerName
            ? _value.customerName
            : customerName // ignore: cast_nullable_to_non_nullable
                  as String?,
        complainCount: freezed == complainCount
            ? _value.complainCount
            : complainCount // ignore: cast_nullable_to_non_nullable
                  as int?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductListItemImpl implements _ProductListItem {
  const _$ProductListItemImpl({
    this.productId,
    required this.productName,
    required this.brand,
    this.description,
    this.modelNumber,
    this.serialNumber,
    this.dateOfPurchase,
    this.customerId,
    this.customerName,
    this.complainCount,
  });

  factory _$ProductListItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductListItemImplFromJson(json);

  @override
  final int? productId;
  @override
  final String productName;
  @override
  final String brand;
  @override
  final String? description;
  @override
  final String? modelNumber;
  @override
  final String? serialNumber;
  @override
  final DateTime? dateOfPurchase;
  @override
  final int? customerId;
  @override
  final String? customerName;
  @override
  final int? complainCount;

  @override
  String toString() {
    return 'ProductListItem(productId: $productId, productName: $productName, brand: $brand, description: $description, modelNumber: $modelNumber, serialNumber: $serialNumber, dateOfPurchase: $dateOfPurchase, customerId: $customerId, customerName: $customerName, complainCount: $complainCount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductListItemImpl &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.brand, brand) || other.brand == brand) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.modelNumber, modelNumber) ||
                other.modelNumber == modelNumber) &&
            (identical(other.serialNumber, serialNumber) ||
                other.serialNumber == serialNumber) &&
            (identical(other.dateOfPurchase, dateOfPurchase) ||
                other.dateOfPurchase == dateOfPurchase) &&
            (identical(other.customerId, customerId) ||
                other.customerId == customerId) &&
            (identical(other.customerName, customerName) ||
                other.customerName == customerName) &&
            (identical(other.complainCount, complainCount) ||
                other.complainCount == complainCount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    productId,
    productName,
    brand,
    description,
    modelNumber,
    serialNumber,
    dateOfPurchase,
    customerId,
    customerName,
    complainCount,
  );

  /// Create a copy of ProductListItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductListItemImplCopyWith<_$ProductListItemImpl> get copyWith =>
      __$$ProductListItemImplCopyWithImpl<_$ProductListItemImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductListItemImplToJson(this);
  }
}

abstract class _ProductListItem implements ProductListItem {
  const factory _ProductListItem({
    final int? productId,
    required final String productName,
    required final String brand,
    final String? description,
    final String? modelNumber,
    final String? serialNumber,
    final DateTime? dateOfPurchase,
    final int? customerId,
    final String? customerName,
    final int? complainCount,
  }) = _$ProductListItemImpl;

  factory _ProductListItem.fromJson(Map<String, dynamic> json) =
      _$ProductListItemImpl.fromJson;

  @override
  int? get productId;
  @override
  String get productName;
  @override
  String get brand;
  @override
  String? get description;
  @override
  String? get modelNumber;
  @override
  String? get serialNumber;
  @override
  DateTime? get dateOfPurchase;
  @override
  int? get customerId;
  @override
  String? get customerName;
  @override
  int? get complainCount;

  /// Create a copy of ProductListItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductListItemImplCopyWith<_$ProductListItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductSearchResponse _$ProductSearchResponseFromJson(
  Map<String, dynamic> json,
) {
  return _ProductSearchResponse.fromJson(json);
}

/// @nodoc
mixin _$ProductSearchResponse {
  int get responseCode => throw _privateConstructorUsedError;
  String get responseMessage => throw _privateConstructorUsedError;
  List<ProductListItem>? get response => throw _privateConstructorUsedError;
  bool? get success => throw _privateConstructorUsedError;
  int? get totalElements => throw _privateConstructorUsedError;
  int? get totalPages => throw _privateConstructorUsedError;
  int? get currentPage => throw _privateConstructorUsedError;

  /// Serializes this ProductSearchResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductSearchResponseCopyWith<ProductSearchResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductSearchResponseCopyWith<$Res> {
  factory $ProductSearchResponseCopyWith(
    ProductSearchResponse value,
    $Res Function(ProductSearchResponse) then,
  ) = _$ProductSearchResponseCopyWithImpl<$Res, ProductSearchResponse>;
  @useResult
  $Res call({
    int responseCode,
    String responseMessage,
    List<ProductListItem>? response,
    bool? success,
    int? totalElements,
    int? totalPages,
    int? currentPage,
  });
}

/// @nodoc
class _$ProductSearchResponseCopyWithImpl<
  $Res,
  $Val extends ProductSearchResponse
>
    implements $ProductSearchResponseCopyWith<$Res> {
  _$ProductSearchResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? responseCode = null,
    Object? responseMessage = null,
    Object? response = freezed,
    Object? success = freezed,
    Object? totalElements = freezed,
    Object? totalPages = freezed,
    Object? currentPage = freezed,
  }) {
    return _then(
      _value.copyWith(
            responseCode: null == responseCode
                ? _value.responseCode
                : responseCode // ignore: cast_nullable_to_non_nullable
                      as int,
            responseMessage: null == responseMessage
                ? _value.responseMessage
                : responseMessage // ignore: cast_nullable_to_non_nullable
                      as String,
            response: freezed == response
                ? _value.response
                : response // ignore: cast_nullable_to_non_nullable
                      as List<ProductListItem>?,
            success: freezed == success
                ? _value.success
                : success // ignore: cast_nullable_to_non_nullable
                      as bool?,
            totalElements: freezed == totalElements
                ? _value.totalElements
                : totalElements // ignore: cast_nullable_to_non_nullable
                      as int?,
            totalPages: freezed == totalPages
                ? _value.totalPages
                : totalPages // ignore: cast_nullable_to_non_nullable
                      as int?,
            currentPage: freezed == currentPage
                ? _value.currentPage
                : currentPage // ignore: cast_nullable_to_non_nullable
                      as int?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ProductSearchResponseImplCopyWith<$Res>
    implements $ProductSearchResponseCopyWith<$Res> {
  factory _$$ProductSearchResponseImplCopyWith(
    _$ProductSearchResponseImpl value,
    $Res Function(_$ProductSearchResponseImpl) then,
  ) = __$$ProductSearchResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int responseCode,
    String responseMessage,
    List<ProductListItem>? response,
    bool? success,
    int? totalElements,
    int? totalPages,
    int? currentPage,
  });
}

/// @nodoc
class __$$ProductSearchResponseImplCopyWithImpl<$Res>
    extends
        _$ProductSearchResponseCopyWithImpl<$Res, _$ProductSearchResponseImpl>
    implements _$$ProductSearchResponseImplCopyWith<$Res> {
  __$$ProductSearchResponseImplCopyWithImpl(
    _$ProductSearchResponseImpl _value,
    $Res Function(_$ProductSearchResponseImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProductSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? responseCode = null,
    Object? responseMessage = null,
    Object? response = freezed,
    Object? success = freezed,
    Object? totalElements = freezed,
    Object? totalPages = freezed,
    Object? currentPage = freezed,
  }) {
    return _then(
      _$ProductSearchResponseImpl(
        responseCode: null == responseCode
            ? _value.responseCode
            : responseCode // ignore: cast_nullable_to_non_nullable
                  as int,
        responseMessage: null == responseMessage
            ? _value.responseMessage
            : responseMessage // ignore: cast_nullable_to_non_nullable
                  as String,
        response: freezed == response
            ? _value._response
            : response // ignore: cast_nullable_to_non_nullable
                  as List<ProductListItem>?,
        success: freezed == success
            ? _value.success
            : success // ignore: cast_nullable_to_non_nullable
                  as bool?,
        totalElements: freezed == totalElements
            ? _value.totalElements
            : totalElements // ignore: cast_nullable_to_non_nullable
                  as int?,
        totalPages: freezed == totalPages
            ? _value.totalPages
            : totalPages // ignore: cast_nullable_to_non_nullable
                  as int?,
        currentPage: freezed == currentPage
            ? _value.currentPage
            : currentPage // ignore: cast_nullable_to_non_nullable
                  as int?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductSearchResponseImpl implements _ProductSearchResponse {
  const _$ProductSearchResponseImpl({
    required this.responseCode,
    required this.responseMessage,
    final List<ProductListItem>? response,
    this.success,
    this.totalElements,
    this.totalPages,
    this.currentPage,
  }) : _response = response;

  factory _$ProductSearchResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductSearchResponseImplFromJson(json);

  @override
  final int responseCode;
  @override
  final String responseMessage;
  final List<ProductListItem>? _response;
  @override
  List<ProductListItem>? get response {
    final value = _response;
    if (value == null) return null;
    if (_response is EqualUnmodifiableListView) return _response;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? success;
  @override
  final int? totalElements;
  @override
  final int? totalPages;
  @override
  final int? currentPage;

  @override
  String toString() {
    return 'ProductSearchResponse(responseCode: $responseCode, responseMessage: $responseMessage, response: $response, success: $success, totalElements: $totalElements, totalPages: $totalPages, currentPage: $currentPage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductSearchResponseImpl &&
            (identical(other.responseCode, responseCode) ||
                other.responseCode == responseCode) &&
            (identical(other.responseMessage, responseMessage) ||
                other.responseMessage == responseMessage) &&
            const DeepCollectionEquality().equals(other._response, _response) &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.totalElements, totalElements) ||
                other.totalElements == totalElements) &&
            (identical(other.totalPages, totalPages) ||
                other.totalPages == totalPages) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    responseCode,
    responseMessage,
    const DeepCollectionEquality().hash(_response),
    success,
    totalElements,
    totalPages,
    currentPage,
  );

  /// Create a copy of ProductSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductSearchResponseImplCopyWith<_$ProductSearchResponseImpl>
  get copyWith =>
      __$$ProductSearchResponseImplCopyWithImpl<_$ProductSearchResponseImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductSearchResponseImplToJson(this);
  }
}

abstract class _ProductSearchResponse implements ProductSearchResponse {
  const factory _ProductSearchResponse({
    required final int responseCode,
    required final String responseMessage,
    final List<ProductListItem>? response,
    final bool? success,
    final int? totalElements,
    final int? totalPages,
    final int? currentPage,
  }) = _$ProductSearchResponseImpl;

  factory _ProductSearchResponse.fromJson(Map<String, dynamic> json) =
      _$ProductSearchResponseImpl.fromJson;

  @override
  int get responseCode;
  @override
  String get responseMessage;
  @override
  List<ProductListItem>? get response;
  @override
  bool? get success;
  @override
  int? get totalElements;
  @override
  int? get totalPages;
  @override
  int? get currentPage;

  /// Create a copy of ProductSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductSearchResponseImplCopyWith<_$ProductSearchResponseImpl>
  get copyWith => throw _privateConstructorUsedError;
}
