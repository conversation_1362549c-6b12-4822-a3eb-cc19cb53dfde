import 'dart:io';
import 'package:dio/dio.dart';

/// Test script to verify API connectivity and response structure
/// Run with: dart test_api_connection.dart
void main() async {
  print('🔍 Testing API Connection and Response Structure...\n');

  final dio = Dio();
  dio.options.baseUrl = 'https://grumble.co.in/api/';
  dio.options.connectTimeout = const Duration(seconds: 30);
  dio.options.receiveTimeout = const Duration(seconds: 30);

  // Test 1: Health Check
  await testHealthCheck(dio);

  // Test 2: Authentication Endpoint
  await testAuthEndpoint(dio);

  // Test 3: CORS Headers
  await testCorsHeaders(dio);

  print('\n✅ API Testing Complete!');
}

Future<void> testHealthCheck(Dio dio) async {
  print('1️⃣ Testing Base URL Connectivity...');
  try {
    final response = await dio.get('/');
    print('   ✅ Base URL accessible');
    print('   📊 Status: ${response.statusCode}');
    print('   🔧 Headers: ${response.headers.map}');
  } catch (e) {
    print('   ❌ Base URL test failed: $e');
  }
  print('');
}

Future<void> testAuthEndpoint(Dio dio) async {
  print('2️⃣ Testing Authentication Endpoint...');
  try {
    // Test with dummy credentials to check endpoint structure
    final response = await dio.post(
      '/auth-service/auth/authenticate',
      data: {'username': 'testuser', 'password': 'testpass'},
      options: Options(
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        validateStatus: (status) => true, // Accept all status codes
      ),
    );

    print('   📊 Status Code: ${response.statusCode}');
    print('   📝 Response Headers:');
    response.headers.map.forEach((key, value) {
      print('      $key: ${value.join(', ')}');
    });

    print('   📄 Response Body:');
    print('      ${response.data}');

    // Validate response structure
    if (response.data is Map<String, dynamic>) {
      final data = response.data as Map<String, dynamic>;
      print('   🔍 Response Structure Analysis:');
      print('      - Has responseCode: ${data.containsKey('responseCode')}');
      print(
        '      - Has responseMessage: ${data.containsKey('responseMessage')}',
      );
      print(
        '      - Has data/response: ${data.containsKey('data') || data.containsKey('response')}',
      );

      if (data.containsKey('data') || data.containsKey('response')) {
        final authData = data['data'] ?? data['response'];
        if (authData is Map<String, dynamic>) {
          print('      - Auth data structure:');
          print(
            '        * Has accessToken: ${authData.containsKey('accessToken')}',
          );
          print('        * Has userType: ${authData.containsKey('userType')}');
          print(
            '        * Has officeCode: ${authData.containsKey('officeCode')}',
          );
          print('        * Has userId: ${authData.containsKey('userId')}');
          print('        * Has roleId: ${authData.containsKey('roleId')}');
        }
      }
    }
  } catch (e) {
    print('   ❌ Auth endpoint test failed: $e');
    if (e is DioException) {
      print('   🔧 Error details:');
      print('      - Type: ${e.type}');
      print('      - Message: ${e.message}');
      print('      - Response: ${e.response?.data}');
    }
  }
  print('');
}

Future<void> testCorsHeaders(Dio dio) async {
  print('3️⃣ Testing CORS Configuration...');
  try {
    final response = await dio.request(
      '/auth-service/auth/authenticate',
      options: Options(
        method: 'OPTIONS',
        headers: {
          'Origin': 'https://grumble.co.in',
          'Access-Control-Request-Method': 'POST',
          'Access-Control-Request-Headers': 'Content-Type,Authorization',
        },
        validateStatus: (status) => true,
      ),
    );

    print('   📊 CORS Preflight Status: ${response.statusCode}');
    print('   🔧 CORS Headers:');
    final corsHeaders = [
      'access-control-allow-origin',
      'access-control-allow-methods',
      'access-control-allow-headers',
      'access-control-max-age',
    ];

    for (final header in corsHeaders) {
      final value = response.headers.value(header);
      print('      $header: ${value ?? 'Not set'}');
    }
  } catch (e) {
    print('   ❌ CORS test failed: $e');
  }
  print('');
}
