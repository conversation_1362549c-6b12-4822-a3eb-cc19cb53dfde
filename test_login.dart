import 'package:dio/dio.dart';

/// Test login with actual credentials
/// Run with: dart test_login.dart
void main() async {
  print('🔐 Testing Login with Production API...\n');

  final dio = Dio();
  dio.options.baseUrl = 'http://46.37.122.247:9502/';
  dio.options.connectTimeout = const Duration(seconds: 30);
  dio.options.receiveTimeout = const Duration(seconds: 30);

  // Test login with demo credentials
  await testLogin(dio, 'Testww', 'Admin@1234');

  print('\n✅ Login Testing Complete!');
}

Future<void> testLogin(Dio dio, String username, String password) async {
  print('🔐 Testing Login Endpoint...');
  print('   Username: $username');
  print('   Password: $password');

  try {
    final response = await dio.post(
      '/auth-service/auth/authenticate',
      data: {'username': username, 'password': password},
      options: Options(
        headers: {'accept': '*/*', 'Content-Type': 'application/json'},
        validateStatus: (status) => true, // Accept all status codes
      ),
    );

    print('   📊 Status Code: ${response.statusCode}');
    print('   📄 Response Body:');
    print('      ${response.data}');

    // Analyze response structure (direct format)
    if (response.data is Map<String, dynamic>) {
      final data = response.data as Map<String, dynamic>;
      print('   🔍 Response Analysis:');

      if (response.statusCode == 200 && data.containsKey('accessToken')) {
        print('   ✅ Login Successful!');
        print('   🎫 Token Information:');
        print(
          '      - Access Token: ${data['accessToken']?.toString().substring(0, 20)}...',
        );
        print('      - Token Type: ${data['tokenType']}');
        print('      - Office Code: ${data['officeCode']}');
      } else {
        print('   ❌ Login Failed');
        print(
          '      Error: ${data['message'] ?? data['error'] ?? 'Unknown error'}',
        );
      }
    }
  } catch (e) {
    print('   ❌ Login test failed: $e');
    if (e is DioException) {
      print('   🔧 Error details:');
      print('      - Type: ${e.type}');
      print('      - Message: ${e.message}');
      if (e.response?.data != null) {
        print('      - Response: ${e.response!.data}');
      }
    }
  }
}
