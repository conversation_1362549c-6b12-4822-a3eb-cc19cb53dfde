# Standard Operating Procedure (SOP)
## Complaint Management Mobile Application

### 📱 **Application Overview**

The Complaint Management Mobile Application is a Flutter-based mobile app that provides a comprehensive solution for managing customer complaints, service requests, and technician assignments. The app is designed to work seamlessly with the existing Spring Boot backend API and mirrors the functionality of the React.js web application.

### 🏗️ **System Architecture**

#### **Technology Stack**
- **Frontend**: Flutter (Dart)
- **State Management**: Riverpod
- **HTTP Client**: Dio
- **Navigation**: GoRouter
- **Local Storage**: SharedPreferences + FlutterSecureStorage
- **Backend**: Spring Boot API (Java)
- **Database**: MySQL

#### **Development Environment (Working Configuration)**
- **API Base URL**: `http://46.37.122.247:9502/`
- **Auth Service**: `auth-service/auth/authenticate`
- **Complaint Service**: `complain-service`
- **Environment**: Development (HTTP)
- **Production URL**: `https://grumble.co.in/api/` (available but not configured)

#### **Working Credentials**
- **Username**: `Testww`
- **Password**: `Admin@1234`

### 🚀 **Setup Instructions**

#### **Prerequisites**
1. Flutter SDK (3.0+)
2. Dart SDK (3.0+)
3. Android Studio / VS Code
4. Git

#### **Installation Steps**

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd complain_mobile
   ```

2. **Install Dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate Code (Required for Freezed models)**
   ```bash
   dart run build_runner build --delete-conflicting-outputs
   ```

4. **Run the Application**
   ```bash
   # For Windows Desktop (Development)
   flutter run -d windows
   
   # For Android Device/Emulator
   flutter run -d android
   
   # For iOS Device/Simulator (macOS only)
   flutter run -d ios
   ```

### 📋 **Application Features**

#### **1. Authentication System**
- **Login Screen**: Username/password authentication
- **JWT Token Management**: Secure token storage and automatic refresh
- **Role-based Access**: Admin, Employee, User roles
- **Auto-logout**: Handles token expiration

#### **2. Complaint Management**
- **Create Complaints**: Multi-step form (Customer → Product → Complaint)
- **View Complaints**: Paginated list with search and filters
- **Complaint Details**: Complete information with action buttons
- **Status Management**: Track complaint lifecycle
- **Assignment**: Assign complaints to employees
- **Visit Management**: Create and track service visits
- **Close Complaints**: Mark complaints as resolved

#### **3. Customer Management**
- **Customer Directory**: Search and view customer information
- **Customer Details**: Contact information and complaint history
- **Create Customers**: Add new customers to the system

#### **4. Employee Management**
- **Employee Directory**: View all technicians and staff
- **Assignment Tracking**: See employee workload and assignments

#### **5. Search & Filters**
- **Global Search**: Search across complaints, customers
- **Advanced Filters**: Filter by status, date range, employee
- **Real-time Results**: Instant search results

### 🔧 **Configuration**

#### **API Configuration**
File: `lib/core/constants/app_constants.dart`

```dart
class AppConstants {
  // API Configuration - Development Environment
  static const String baseUrl = 'http://46.37.122.247:9502/';
  static const String authServicePath = 'auth-service/auth';
  static const String complainServicePath = 'complain-service';
  
  // Storage Keys (matching React.js pattern)
  static const String tokenKey = 'authToken_grumble';
  static const String userKey = 'username_grumble';
  static const String officeCodeKey = 'officeCode';
}
```

#### **Environment-specific Configuration**
- **Development**: Current configuration
- **Production**: Update `baseUrl` to production server
- **Staging**: Update `baseUrl` to staging server

### 📱 **User Interface Flow**

#### **1. App Launch Flow**
```
Splash Screen → Check Authentication → Login/Dashboard
```

#### **2. Authentication Flow**
```
Login Screen → API Authentication → Store JWT Token → Navigate to Dashboard
```

#### **3. Complaint Creation Flow**
```
Complaint List → Create Button → Customer Form → Product Form → Complaint Form → Submit
```

#### **4. Complaint Management Flow**
```
Complaint List → Select Complaint → View Details → Actions (Assign/Visit/Close)
```

### 🔐 **Security Features**

#### **Authentication**
- JWT token-based authentication
- Secure token storage using FlutterSecureStorage
- Automatic token refresh
- Session timeout handling

#### **API Security**
- Bearer token authentication
- HTTPS communication (production)
- Request/response validation
- Error handling and logging

#### **Data Protection**
- Local data encryption
- Secure storage for sensitive information
- No sensitive data in logs

### 📊 **API Integration**

#### **Authentication Endpoints**
```
POST /auth-service/auth/authenticate
- Request: { "username": "string", "password": "string" }
- Response: { "accessToken": "jwt_token", "tokenType": "Bearer", "officeCode": "string" }
- Headers: { "accept": "*/*", "Content-Type": "application/json" }
```

#### **Complaint Endpoints**
```
POST /complain-service/user/complain/search
POST /complain-service/user/complain/create
GET  /complain-service/user/complain/view/{id}
POST /complain-service/user/complain/assign/{complainId}/{employeeId}
POST /complain-service/user/complain/visit/{complainId}
POST /complain-service/user/complain/close/{complainId}
```

#### **Customer Endpoints**
```
POST /complain-service/user/cust/search
POST /complain-service/user/cust/create
GET  /complain-service/user/cust/search/{id}
```

#### **Employee Endpoints**
```
POST /complain-service/user/emp/search
POST /complain-service/user/emp/create
```

### 🧪 **Testing**

#### **Running Tests**
```bash
# Unit Tests
flutter test

# Integration Tests
flutter test integration_test/

# Widget Tests
flutter test test/widget_test.dart
```

#### **Test Coverage**
- Unit tests for providers and services
- Widget tests for UI components
- Integration tests for complete flows

### 🚀 **Deployment**

#### **Development Build**
```bash
flutter run -d windows  # Desktop testing
flutter run -d android  # Android testing
```

#### **Production Build**
```bash
# Android APK
flutter build apk --release

# Android App Bundle
flutter build appbundle --release

# iOS (macOS only)
flutter build ios --release
```

### 🔧 **Troubleshooting**

#### **Common Issues**

1. **Build Runner Issues**
   ```bash
   flutter clean
   flutter pub get
   dart run build_runner clean
   dart run build_runner build --delete-conflicting-outputs
   ```

2. **API Connection Issues**
   - Check network connectivity
   - Verify API base URL in constants
   - Check server status

3. **Authentication Issues**
   - Clear app data/cache
   - Check token expiration
   - Verify credentials

#### **Debug Mode**
```bash
flutter run --debug -d windows
```

#### **Logs and Debugging**
- Use Flutter Inspector for UI debugging
- Check console logs for API errors
- Use breakpoints for code debugging

### 📈 **Performance Optimization**

#### **Best Practices**
1. **Image Optimization**: Use cached network images
2. **List Performance**: Implement pagination and lazy loading
3. **State Management**: Use Riverpod efficiently
4. **Memory Management**: Dispose controllers properly
5. **API Caching**: Cache frequently accessed data

#### **Monitoring**
- Monitor app performance metrics
- Track API response times
- Monitor memory usage
- Track user engagement

### 🔄 **Maintenance**

#### **Regular Updates**
1. Update Flutter SDK regularly
2. Update dependencies
3. Security patches
4. Bug fixes and improvements

#### **Backup and Recovery**
- Regular code backups
- Database backups
- Configuration backups

### 📞 **Support**

#### **Technical Support**
- Development Team: [Contact Information]
- API Documentation: [Backend API Docs]
- Flutter Documentation: https://flutter.dev/docs

#### **User Support**
- User Manual: [Link to User Guide]
- Training Materials: [Link to Training]
- Help Desk: [Contact Information]

---

**Document Version**: 1.0  
**Last Updated**: [Current Date]  
**Prepared By**: Development Team  
**Approved By**: Project Manager
