import 'package:freezed_annotation/freezed_annotation.dart';
import 'customer.dart';
import 'product.dart';
import 'employee.dart';

part 'complaint.freezed.dart';
part 'complaint.g.dart';

@freezed
class Complaint with _$Complaint {
  const factory Complaint({
    int? complainId,
    required String complainNumber,
    required String serviceType,
    required DateTime complainOriginDate,
    required String complainType,
    required String csn,
    required String status,
    Customer? customer,
    Product? product,
    Employee? assignedEmployee,
    DateTime? registerDate,
    DateTime? closeDate,
    String? closingRemark,
    bool? inOut,
    bool? customerMisuse,
    List<Visit>? visits,
  }) = _Complaint;

  factory Complaint.fromJson(Map<String, dynamic> json) => _$ComplaintFromJson(json);
}

@freezed
class ComplaintListItem with _$ComplaintListItem {
  const factory ComplaintListItem({
    int? complainId,
    required String complainNumber,
    required String serviceType,
    required DateTime complainOriginDate,
    required String complainType,
    required String csn,
    required String status,
    String? customerName,
    String? customerMobile,
    String? productName,
    String? productBrand,
    String? employeeName,
    DateTime? registerDate,
    DateTime? closeDate,
    String? closingRemark,
    bool? inOut,
    bool? customerMisuse,
  }) = _ComplaintListItem;

  factory ComplaintListItem.fromJson(Map<String, dynamic> json) => 
      _$ComplaintListItemFromJson(json);
}

@freezed
class ComplaintCreateRequest with _$ComplaintCreateRequest {
  const factory ComplaintCreateRequest({
    required CustomerCreateRequest customerCreateRequest,
    required ProductCreateRequest productCreateRequest,
    required ComplaintRegisterRequest complainRegisterRequest,
  }) = _ComplaintCreateRequest;

  factory ComplaintCreateRequest.fromJson(Map<String, dynamic> json) => 
      _$ComplaintCreateRequestFromJson(json);
}

@freezed
class ComplaintRegisterRequest with _$ComplaintRegisterRequest {
  const factory ComplaintRegisterRequest({
    required String complainNumber,
    required String serviceType,
    required DateTime complainOriginDate,
    required String complainType,
    required String csn,
    bool? inOut,
    bool? customerMisuse,
  }) = _ComplaintRegisterRequest;

  factory ComplaintRegisterRequest.fromJson(Map<String, dynamic> json) => 
      _$ComplaintRegisterRequestFromJson(json);
}

@freezed
class ComplaintSearchRequest with _$ComplaintSearchRequest {
  const factory ComplaintSearchRequest({
    String? complainNumber,
    String? customerName,
    String? customerMobile,
    String? status,
    DateTime? fromDate,
    DateTime? toDate,
    int? page,
    int? size,
    String? sortBy,
    String? sortDirection,
  }) = _ComplaintSearchRequest;

  factory ComplaintSearchRequest.fromJson(Map<String, dynamic> json) => 
      _$ComplaintSearchRequestFromJson(json);
}

@freezed
class ComplaintSearchResponse with _$ComplaintSearchResponse {
  const factory ComplaintSearchResponse({
    required int responseCode,
    required String responseMessage,
    List<ComplaintListItem>? response,
    bool? success,
    int? totalElements,
    int? totalPages,
    int? currentPage,
  }) = _ComplaintSearchResponse;

  factory ComplaintSearchResponse.fromJson(Map<String, dynamic> json) => 
      _$ComplaintSearchResponseFromJson(json);
}

@freezed
class Visit with _$Visit {
  const factory Visit({
    int? visitId,
    int? complainId,
    DateTime? visitDate,
    String? visitStatus,
    int? amountCollected,
    String? technicianRemark,
    bool? inOut,
    bool? customerMisuse,
    Employee? technician,
  }) = _Visit;

  factory Visit.fromJson(Map<String, dynamic> json) => _$VisitFromJson(json);
}

@freezed
class ComplaintTransferRequest with _$ComplaintTransferRequest {
  const factory ComplaintTransferRequest({
    required String visitStatus,
    required int amountCollected,
    bool? inOut,
    bool? customerMisuse,
    required String technicianRemark,
    required DateTime visitDate,
  }) = _ComplaintTransferRequest;

  factory ComplaintTransferRequest.fromJson(Map<String, dynamic> json) => 
      _$ComplaintTransferRequestFromJson(json);
}

@freezed
class CloseComplaintRequest with _$CloseComplaintRequest {
  const factory CloseComplaintRequest({
    required String closingRemark,
    required DateTime closeDate,
  }) = _CloseComplaintRequest;

  factory CloseComplaintRequest.fromJson(Map<String, dynamic> json) => 
      _$CloseComplaintRequestFromJson(json);
}
