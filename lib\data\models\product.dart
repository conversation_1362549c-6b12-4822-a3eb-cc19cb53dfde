import 'package:freezed_annotation/freezed_annotation.dart';

part 'product.freezed.dart';
part 'product.g.dart';

@freezed
class Product with _$Product {
  const factory Product({
    int? productId,
    required String productName,
    required String brand,
    String? description,
    String? modelNumber,
    String? serialNumber,
    DateTime? dateOfPurchase,
    int? customerId,
  }) = _Product;

  factory Product.fromJson(Map<String, dynamic> json) => _$ProductFromJson(json);
}

@freezed
class ProductCreateRequest with _$ProductCreateRequest {
  const factory ProductCreateRequest({
    required String productName,
    required String brand,
    String? description,
    String? modelNumber,
    String? serialNumber,
    DateTime? dateOfPurchase,
  }) = _ProductCreateRequest;

  factory ProductCreateRequest.fromJson(Map<String, dynamic> json) => 
      _$ProductCreateRequestFromJson(json);
}

@freezed
class ProductListItem with _$ProductListItem {
  const factory ProductListItem({
    int? productId,
    required String productName,
    required String brand,
    String? description,
    String? modelNumber,
    String? serialNumber,
    DateTime? dateOfPurchase,
    int? customerId,
    String? customerName,
    int? complainCount,
  }) = _ProductListItem;

  factory ProductListItem.fromJson(Map<String, dynamic> json) => 
      _$ProductListItemFromJson(json);
}

@freezed
class ProductSearchResponse with _$ProductSearchResponse {
  const factory ProductSearchResponse({
    required int responseCode,
    required String responseMessage,
    List<ProductListItem>? response,
    bool? success,
    int? totalElements,
    int? totalPages,
    int? currentPage,
  }) = _ProductSearchResponse;

  factory ProductSearchResponse.fromJson(Map<String, dynamic> json) => 
      _$ProductSearchResponseFromJson(json);
}
