import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../../core/constants/app_constants.dart';

class StorageService {
  static const _storage = FlutterSecureStorage();

  // Token Management
  static Future<void> saveToken(String token) async {
    await _storage.write(key: AppConstants.tokenKey, value: token);
  }

  static Future<String?> getToken() async {
    return await _storage.read(key: AppConstants.tokenKey);
  }

  static Future<void> deleteToken() async {
    await _storage.delete(key: AppConstants.tokenKey);
  }

  // User Management
  static Future<void> saveUser(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.userKey, jsonEncode(user.toJson()));
  }

  static Future<User?> getUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userData = prefs.getString(AppConstants.userKey);
    if (userData != null) {
      return User.fromJson(jsonDecode(userData));
    }
    return null;
  }

  static Future<void> deleteUser() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.userKey);
  }

  // Clear All Data
  static Future<void> clearAll() async {
    await deleteToken();
    await deleteUser();
  }

  // App Preferences
  static Future<void> setFirstLaunch(bool isFirst) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('first_launch', isFirst);
  }

  static Future<bool> isFirstLaunch() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('first_launch') ?? true;
  }

  static Future<void> setThemeMode(String mode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('theme_mode', mode);
  }

  static Future<String> getThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('theme_mode') ?? 'system';
  }

  // Office Code Management (for API headers)
  static Future<void> saveOfficeCode(String officeCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.officeCodeKey, officeCode);
  }

  static Future<String?> getOfficeCode() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(AppConstants.officeCodeKey);
  }

  static Future<void> deleteOfficeCode() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.officeCodeKey);
  }
}
