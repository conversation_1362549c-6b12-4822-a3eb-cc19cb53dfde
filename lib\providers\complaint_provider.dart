import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../data/models/complaint.dart';
import '../data/services/api_service.dart';
import '../core/constants/app_constants.dart';

// Complaint List State
class ComplaintListState {
  final List<ComplaintListItem> complaints;
  final bool isLoading;
  final bool isLoadingMore;
  final String? error;
  final int currentPage;
  final int totalPages;
  final bool hasMore;

  const ComplaintListState({
    this.complaints = const [],
    this.isLoading = false,
    this.isLoadingMore = false,
    this.error,
    this.currentPage = 0,
    this.totalPages = 0,
    this.hasMore = true,
  });

  ComplaintListState copyWith({
    List<ComplaintListItem>? complaints,
    bool? isLoading,
    bool? isLoadingMore,
    String? error,
    int? currentPage,
    int? totalPages,
    bool? hasMore,
  }) {
    return ComplaintListState(
      complaints: complaints ?? this.complaints,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      error: error,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      hasMore: hasMore ?? this.hasMore,
    );
  }
}

// Complaint List Notifier
class ComplaintListNotifier extends StateNotifier<ComplaintListState> {
  final ApiService _apiService;
  ComplaintSearchRequest? _lastSearchRequest;

  ComplaintListNotifier(this._apiService) : super(const ComplaintListState());

  Future<void> searchComplaints({
    String? complainNumber,
    String? customerName,
    String? customerMobile,
    String? status,
    DateTime? fromDate,
    DateTime? toDate,
    bool refresh = false,
  }) async {
    if (refresh) {
      state = state.copyWith(isLoading: true, error: null);
    } else if (state.isLoading || state.isLoadingMore) {
      return;
    } else {
      state = state.copyWith(isLoadingMore: true, error: null);
    }

    final page = refresh ? 0 : state.currentPage + 1;
    
    final searchRequest = ComplaintSearchRequest(
      complainNumber: complainNumber,
      customerName: customerName,
      customerMobile: customerMobile,
      status: status,
      fromDate: fromDate,
      toDate: toDate,
      page: page,
      size: AppConstants.defaultPageSize,
      sortBy: 'registerDate',
      sortDirection: 'DESC',
    );

    _lastSearchRequest = searchRequest;

    try {
      final response = await _apiService.searchComplaints(searchRequest);
      
      if (response.success == true && response.response != null) {
        final newComplaints = response.response!;
        final complaints = refresh 
            ? newComplaints 
            : [...state.complaints, ...newComplaints];
        
        state = state.copyWith(
          complaints: complaints,
          isLoading: false,
          isLoadingMore: false,
          currentPage: page,
          totalPages: response.totalPages ?? 0,
          hasMore: page < (response.totalPages ?? 0) - 1,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          isLoadingMore: false,
          error: response.responseMessage,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        isLoadingMore: false,
        error: e.toString(),
      );
    }
  }

  Future<void> loadMore() async {
    if (state.hasMore && !state.isLoadingMore && _lastSearchRequest != null) {
      await searchComplaints(
        complainNumber: _lastSearchRequest!.complainNumber,
        customerName: _lastSearchRequest!.customerName,
        customerMobile: _lastSearchRequest!.customerMobile,
        status: _lastSearchRequest!.status,
        fromDate: _lastSearchRequest!.fromDate,
        toDate: _lastSearchRequest!.toDate,
        refresh: false,
      );
    }
  }

  Future<void> refresh() async {
    if (_lastSearchRequest != null) {
      await searchComplaints(
        complainNumber: _lastSearchRequest!.complainNumber,
        customerName: _lastSearchRequest!.customerName,
        customerMobile: _lastSearchRequest!.customerMobile,
        status: _lastSearchRequest!.status,
        fromDate: _lastSearchRequest!.fromDate,
        toDate: _lastSearchRequest!.toDate,
        refresh: true,
      );
    } else {
      await searchComplaints(refresh: true);
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Complaint Detail State
class ComplaintDetailState {
  final Complaint? complaint;
  final bool isLoading;
  final String? error;

  const ComplaintDetailState({
    this.complaint,
    this.isLoading = false,
    this.error,
  });

  ComplaintDetailState copyWith({
    Complaint? complaint,
    bool? isLoading,
    String? error,
  }) {
    return ComplaintDetailState(
      complaint: complaint ?? this.complaint,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Complaint Detail Notifier
class ComplaintDetailNotifier extends StateNotifier<ComplaintDetailState> {
  final ApiService _apiService;

  ComplaintDetailNotifier(this._apiService) : super(const ComplaintDetailState());

  Future<void> loadComplaint(int complainId) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final response = await _apiService.getComplaintById(complainId);
      
      if (response.responseCode == 0 && response.response != null) {
        state = state.copyWith(
          complaint: response.response,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.responseMessage,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<bool> assignComplaint(int complainId, int employeeId) async {
    try {
      final response = await _apiService.assignComplaint(complainId, employeeId);
      
      if (response.success == true) {
        // Reload complaint data
        await loadComplaint(complainId);
        return true;
      } else {
        state = state.copyWith(error: response.responseMessage);
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  Future<bool> createVisit(int complainId, ComplaintTransferRequest request) async {
    try {
      final response = await _apiService.createVisit(complainId, request);
      
      if (response.success == true) {
        // Reload complaint data
        await loadComplaint(complainId);
        return true;
      } else {
        state = state.copyWith(error: response.responseMessage);
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  Future<bool> closeComplaint(int complainId, CloseComplaintRequest request) async {
    try {
      final response = await _apiService.closeComplaint(complainId, request);
      
      if (response.success == true) {
        // Reload complaint data
        await loadComplaint(complainId);
        return true;
      } else {
        state = state.copyWith(error: response.responseMessage);
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Providers
final complaintListProvider = StateNotifierProvider<ComplaintListNotifier, ComplaintListState>((ref) {
  final apiService = ref.read(apiServiceProvider);
  return ComplaintListNotifier(apiService);
});

final complaintDetailProvider = StateNotifierProvider<ComplaintDetailNotifier, ComplaintDetailState>((ref) {
  final apiService = ref.read(apiServiceProvider);
  return ComplaintDetailNotifier(apiService);
});

// Create Complaint Provider
final createComplaintProvider = FutureProvider.family<bool, ComplaintCreateRequest>((ref, request) async {
  final apiService = ref.read(apiServiceProvider);
  try {
    final response = await apiService.createComplaint(request);
    return response.success == true;
  } catch (e) {
    throw e.toString();
  }
});
