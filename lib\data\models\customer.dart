import 'package:freezed_annotation/freezed_annotation.dart';
import 'product.dart';

part 'customer.freezed.dart';
part 'customer.g.dart';

@freezed
class Customer with _$Customer {
  const factory Customer({
    int? customerId,
    required String name,
    required String mobileNo,
    String? residentNo,
    String? address,
    String? city,
    List<Product>? products,
  }) = _Customer;

  factory Customer.fromJson(Map<String, dynamic> json) => _$CustomerFromJson(json);
}

@freezed
class CustomerCreateRequest with _$CustomerCreateRequest {
  const factory CustomerCreateRequest({
    required String name,
    required String mobileNo,
    String? residentNo,
    String? address,
    String? city,
  }) = _CustomerCreateRequest;

  factory CustomerCreateRequest.fromJson(Map<String, dynamic> json) => 
      _$CustomerCreateRequestFromJson(json);
}

@freezed
class CustomerListItem with _$CustomerListItem {
  const factory CustomerListItem({
    int? customerId,
    required String name,
    required String mobileNo,
    String? residentNo,
    String? address,
    String? city,
    int? productCount,
    int? complainCount,
  }) = _CustomerListItem;

  factory CustomerListItem.fromJson(Map<String, dynamic> json) => 
      _$CustomerListItemFromJson(json);
}

@freezed
class CustomerSearchResponse with _$CustomerSearchResponse {
  const factory CustomerSearchResponse({
    required int responseCode,
    required String responseMessage,
    List<CustomerListItem>? response,
    bool? success,
    int? totalElements,
    int? totalPages,
    int? currentPage,
  }) = _CustomerSearchResponse;

  factory CustomerSearchResponse.fromJson(Map<String, dynamic> json) => 
      _$CustomerSearchResponseFromJson(json);
}
