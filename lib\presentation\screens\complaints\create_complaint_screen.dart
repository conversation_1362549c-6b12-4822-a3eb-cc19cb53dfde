import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../../../data/models/complaint.dart';
import '../../../data/models/customer.dart';
import '../../../data/models/product.dart';
import '../../../providers/complaint_provider.dart';
import '../../../core/utils/validators.dart';

class CreateComplaintScreen extends ConsumerStatefulWidget {
  const CreateComplaintScreen({super.key});

  @override
  ConsumerState<CreateComplaintScreen> createState() =>
      _CreateComplaintScreenState();
}

class _CreateComplaintScreenState extends ConsumerState<CreateComplaintScreen> {
  final _pageController = PageController();
  final _customerFormKey = GlobalKey<FormState>();
  final _productFormKey = GlobalKey<FormState>();
  final _complaintFormKey = GlobalKey<FormState>();

  int _currentStep = 0;
  bool _isLoading = false;

  // Customer fields
  final _customerNameController = TextEditingController();
  final _customerMobileController = TextEditingController();
  final _customerResidentController = TextEditingController();
  final _customerAddressController = TextEditingController();
  final _customerCityController = TextEditingController();

  // Product fields
  final _productNameController = TextEditingController();
  final _productBrandController = TextEditingController();
  final _productDescriptionController = TextEditingController();
  final _productModelController = TextEditingController();
  final _productSerialController = TextEditingController();
  DateTime? _purchaseDate;

  // Complaint fields
  final _complainNumberController = TextEditingController();
  final _serviceTypeController = TextEditingController();
  final _csnController = TextEditingController();
  DateTime? _complainOriginDate;
  String? _complainType;
  bool _inOut = true;
  bool _customerMisuse = false;

  @override
  void dispose() {
    _pageController.dispose();
    _customerNameController.dispose();
    _customerMobileController.dispose();
    _customerResidentController.dispose();
    _customerAddressController.dispose();
    _customerCityController.dispose();
    _productNameController.dispose();
    _productBrandController.dispose();
    _productDescriptionController.dispose();
    _productModelController.dispose();
    _productSerialController.dispose();
    _complainNumberController.dispose();
    _serviceTypeController.dispose();
    _csnController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Complaint'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Step Indicator
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                _buildStepIndicator(0, 'Customer', _currentStep >= 0),
                Expanded(child: Container(height: 2, color: Colors.grey[300])),
                _buildStepIndicator(1, 'Product', _currentStep >= 1),
                Expanded(child: Container(height: 2, color: Colors.grey[300])),
                _buildStepIndicator(2, 'Complaint', _currentStep >= 2),
              ],
            ),
          ),

          // Form Content
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentStep = index;
                });
              },
              children: [
                _buildCustomerForm(),
                _buildProductForm(),
                _buildComplaintForm(),
              ],
            ),
          ),

          // Navigation Buttons
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                if (_currentStep > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : _previousStep,
                      child: const Text('Previous'),
                    ),
                  ),
                if (_currentStep > 0) const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _nextStep,
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Text(_currentStep == 2 ? 'Create' : 'Next'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(int step, String title, bool isActive) {
    return Column(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: isActive ? Theme.of(context).primaryColor : Colors.grey[300],
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              '${step + 1}',
              style: TextStyle(
                color: isActive ? Colors.white : Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color: isActive ? Theme.of(context).primaryColor : Colors.grey[600],
            fontWeight: isActive ? FontWeight.w500 : FontWeight.normal,
          ),
        ),
      ],
    );
  }

  Widget _buildCustomerForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _customerFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Customer Information',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 24),
            TextFormField(
              controller: _customerNameController,
              decoration: const InputDecoration(
                labelText: 'Customer Name *',
                prefixIcon: Icon(Icons.person),
              ),
              validator: (value) => Validators.required(value, 'Customer name'),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _customerMobileController,
              decoration: const InputDecoration(
                labelText: 'Mobile Number *',
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
              validator: Validators.mobileNumber,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _customerResidentController,
              decoration: const InputDecoration(
                labelText: 'Resident Number',
                prefixIcon: Icon(Icons.home),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _customerAddressController,
              decoration: const InputDecoration(
                labelText: 'Address',
                prefixIcon: Icon(Icons.location_on),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _customerCityController,
              decoration: const InputDecoration(
                labelText: 'City',
                prefixIcon: Icon(Icons.location_city),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _productFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Product Information',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 24),
            TextFormField(
              controller: _productNameController,
              decoration: const InputDecoration(
                labelText: 'Product Name *',
                prefixIcon: Icon(Icons.devices),
              ),
              validator: (value) => Validators.required(value, 'Product name'),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _productBrandController,
              decoration: const InputDecoration(
                labelText: 'Brand *',
                prefixIcon: Icon(Icons.branding_watermark),
              ),
              validator: (value) => Validators.required(value, 'Brand'),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _productDescriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _productModelController,
              decoration: const InputDecoration(
                labelText: 'Model Number',
                prefixIcon: Icon(Icons.model_training),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _productSerialController,
              decoration: const InputDecoration(
                labelText: 'Serial Number',
                prefixIcon: Icon(Icons.confirmation_number),
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              contentPadding: EdgeInsets.zero,
              leading: const Icon(Icons.calendar_today),
              title: Text(
                _purchaseDate == null
                    ? 'Select Purchase Date'
                    : 'Purchase Date: ${_purchaseDate!.day}/${_purchaseDate!.month}/${_purchaseDate!.year}',
              ),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _selectPurchaseDate,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComplaintForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _complaintFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Complaint Details',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 24),
            TextFormField(
              controller: _complainNumberController,
              decoration: const InputDecoration(
                labelText: 'Complaint Number *',
                prefixIcon: Icon(Icons.confirmation_number),
              ),
              validator: Validators.complainNumber,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _serviceTypeController,
              decoration: const InputDecoration(
                labelText: 'Service Type *',
                prefixIcon: Icon(Icons.build),
              ),
              validator: (value) => Validators.required(value, 'Service type'),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _complainType,
              decoration: const InputDecoration(
                labelText: 'Complaint Type *',
                prefixIcon: Icon(Icons.category),
              ),
              items: const [
                DropdownMenuItem(value: 'Type1', child: Text('Type 1')),
                DropdownMenuItem(value: 'Type2', child: Text('Type 2')),
                DropdownMenuItem(value: 'Type3', child: Text('Type 3')),
              ],
              onChanged: (value) {
                setState(() {
                  _complainType = value;
                });
              },
              validator: (value) =>
                  Validators.required(value, 'Complaint type'),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _csnController,
              decoration: const InputDecoration(
                labelText: 'CSN *',
                prefixIcon: Icon(Icons.qr_code),
              ),
              validator: (value) => Validators.required(value, 'CSN'),
            ),
            const SizedBox(height: 16),
            ListTile(
              contentPadding: EdgeInsets.zero,
              leading: const Icon(Icons.calendar_today),
              title: Text(
                _complainOriginDate == null
                    ? 'Select Complaint Origin Date *'
                    : 'Origin Date: ${_complainOriginDate!.day}/${_complainOriginDate!.month}/${_complainOriginDate!.year}',
              ),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _selectComplainOriginDate,
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              contentPadding: EdgeInsets.zero,
              title: const Text('In Warranty'),
              value: _inOut,
              onChanged: (value) {
                setState(() {
                  _inOut = value;
                });
              },
            ),
            SwitchListTile(
              contentPadding: EdgeInsets.zero,
              title: const Text('Customer Misuse'),
              value: _customerMisuse,
              onChanged: (value) {
                setState(() {
                  _customerMisuse = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  void _previousStep() {
    if (_currentStep > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextStep() {
    if (_currentStep == 0) {
      if (_customerFormKey.currentState!.validate()) {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    } else if (_currentStep == 1) {
      if (_productFormKey.currentState!.validate()) {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    } else if (_currentStep == 2) {
      _submitComplaint();
    }
  }

  Future<void> _selectPurchaseDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _purchaseDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() {
        _purchaseDate = date;
      });
    }
  }

  Future<void> _selectComplainOriginDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _complainOriginDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() {
        _complainOriginDate = date;
      });
    }
  }

  Future<void> _submitComplaint() async {
    if (!_complaintFormKey.currentState!.validate()) {
      return;
    }

    if (_complainOriginDate == null) {
      Fluttertoast.showToast(
        msg: 'Please select complaint origin date',
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final request = ComplaintCreateRequest(
        customerCreateRequest: CustomerCreateRequest(
          name: _customerNameController.text.trim(),
          mobileNo: _customerMobileController.text.trim(),
          residentNo: _customerResidentController.text.trim().isEmpty
              ? null
              : _customerResidentController.text.trim(),
          address: _customerAddressController.text.trim().isEmpty
              ? null
              : _customerAddressController.text.trim(),
          city: _customerCityController.text.trim().isEmpty
              ? null
              : _customerCityController.text.trim(),
        ),
        productCreateRequest: ProductCreateRequest(
          productName: _productNameController.text.trim(),
          brand: _productBrandController.text.trim(),
          description: _productDescriptionController.text.trim().isEmpty
              ? null
              : _productDescriptionController.text.trim(),
          modelNumber: _productModelController.text.trim().isEmpty
              ? null
              : _productModelController.text.trim(),
          serialNumber: _productSerialController.text.trim().isEmpty
              ? null
              : _productSerialController.text.trim(),
          dateOfPurchase: _purchaseDate,
        ),
        complainRegisterRequest: ComplaintRegisterRequest(
          complainNumber: _complainNumberController.text.trim(),
          serviceType: _serviceTypeController.text.trim(),
          complainOriginDate: _complainOriginDate!,
          complainType: _complainType!,
          csn: _csnController.text.trim(),
          inOut: _inOut,
          customerMisuse: _customerMisuse,
        ),
      );

      final success = await ref.read(createComplaintProvider(request).future);

      if (success && mounted) {
        Fluttertoast.showToast(
          msg: 'Complaint created successfully!',
          backgroundColor: Colors.green,
          textColor: Colors.white,
        );

        // Refresh complaint list
        ref.read(complaintListProvider.notifier).refresh();

        context.go('/complaints');
      }
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'Failed to create complaint: $e',
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
