// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'employee.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$EmployeeImpl _$$EmployeeImplFromJson(Map<String, dynamic> json) =>
    _$EmployeeImpl(
      empId: (json['empId'] as num?)?.toInt(),
      name: json['name'] as String,
      phoneNo: json['phoneNo'] as String,
      emailId: json['emailId'] as String?,
      address: json['address'] as String?,
      deleted: json['deleted'] as bool?,
      userId: (json['userId'] as num?)?.toInt(),
      technicianCharge: (json['technicianCharge'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$EmployeeImplToJson(_$EmployeeImpl instance) =>
    <String, dynamic>{
      'empId': instance.empId,
      'name': instance.name,
      'phoneNo': instance.phoneNo,
      'emailId': instance.emailId,
      'address': instance.address,
      'deleted': instance.deleted,
      'userId': instance.userId,
      'technicianCharge': instance.technicianCharge,
    };

_$EmployeeCreateRequestImpl _$$EmployeeCreateRequestImplFromJson(
  Map<String, dynamic> json,
) => _$EmployeeCreateRequestImpl(
  name: json['name'] as String,
  phoneNo: json['phoneNo'] as String,
  emailId: json['emailId'] as String?,
  address: json['address'] as String?,
  technicianCharge: (json['technicianCharge'] as num?)?.toInt(),
  username: json['username'] as String?,
  password: json['password'] as String?,
);

Map<String, dynamic> _$$EmployeeCreateRequestImplToJson(
  _$EmployeeCreateRequestImpl instance,
) => <String, dynamic>{
  'name': instance.name,
  'phoneNo': instance.phoneNo,
  'emailId': instance.emailId,
  'address': instance.address,
  'technicianCharge': instance.technicianCharge,
  'username': instance.username,
  'password': instance.password,
};

_$EmployeeListItemImpl _$$EmployeeListItemImplFromJson(
  Map<String, dynamic> json,
) => _$EmployeeListItemImpl(
  empId: (json['empId'] as num?)?.toInt(),
  name: json['name'] as String,
  phoneNo: json['phoneNo'] as String,
  emailId: json['emailId'] as String?,
  address: json['address'] as String?,
  technicianCharge: (json['technicianCharge'] as num?)?.toInt(),
  username: json['username'] as String?,
  deleted: json['deleted'] as bool?,
  assignedComplaints: (json['assignedComplaints'] as num?)?.toInt(),
  resolvedComplaints: (json['resolvedComplaints'] as num?)?.toInt(),
);

Map<String, dynamic> _$$EmployeeListItemImplToJson(
  _$EmployeeListItemImpl instance,
) => <String, dynamic>{
  'empId': instance.empId,
  'name': instance.name,
  'phoneNo': instance.phoneNo,
  'emailId': instance.emailId,
  'address': instance.address,
  'technicianCharge': instance.technicianCharge,
  'username': instance.username,
  'deleted': instance.deleted,
  'assignedComplaints': instance.assignedComplaints,
  'resolvedComplaints': instance.resolvedComplaints,
};

_$EmployeeSearchResponseImpl _$$EmployeeSearchResponseImplFromJson(
  Map<String, dynamic> json,
) => _$EmployeeSearchResponseImpl(
  responseCode: (json['responseCode'] as num).toInt(),
  responseMessage: json['responseMessage'] as String,
  response: (json['response'] as List<dynamic>?)
      ?.map((e) => EmployeeListItem.fromJson(e as Map<String, dynamic>))
      .toList(),
  success: json['success'] as bool?,
  totalElements: (json['totalElements'] as num?)?.toInt(),
  totalPages: (json['totalPages'] as num?)?.toInt(),
  currentPage: (json['currentPage'] as num?)?.toInt(),
);

Map<String, dynamic> _$$EmployeeSearchResponseImplToJson(
  _$EmployeeSearchResponseImpl instance,
) => <String, dynamic>{
  'responseCode': instance.responseCode,
  'responseMessage': instance.responseMessage,
  'response': instance.response,
  'success': instance.success,
  'totalElements': instance.totalElements,
  'totalPages': instance.totalPages,
  'currentPage': instance.currentPage,
};
