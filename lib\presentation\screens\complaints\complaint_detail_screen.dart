import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../../../providers/complaint_provider.dart';
import '../../../core/utils/date_utils.dart';
import '../../widgets/loading_widget.dart';

class ComplaintDetailScreen extends ConsumerStatefulWidget {
  final int complainId;

  const ComplaintDetailScreen({
    super.key,
    required this.complainId,
  });

  @override
  ConsumerState<ComplaintDetailScreen> createState() => _ComplaintDetailScreenState();
}

class _ComplaintDetailScreenState extends ConsumerState<ComplaintDetailScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(complaintDetailProvider.notifier).loadComplaint(widget.complainId);
    });
  }

  @override
  Widget build(BuildContext context) {
    final detailState = ref.watch(complaintDetailProvider);

    // Listen to errors
    ref.listen<String?>(
      complaintDetailProvider.select((state) => state.error),
      (previous, next) {
        if (next != null) {
          Fluttertoast.showToast(
            msg: next,
            toastLength: Toast.LENGTH_LONG,
            gravity: ToastGravity.BOTTOM,
            backgroundColor: Colors.red,
            textColor: Colors.white,
          );
          ref.read(complaintDetailProvider.notifier).clearError();
        }
      },
    );

    return Scaffold(
      appBar: AppBar(
        title: Text('Complaint #${widget.complainId}'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (detailState.complaint != null)
            PopupMenuButton<String>(
              onSelected: (value) => _handleMenuAction(value, detailState.complaint!),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'assign',
                  child: ListTile(
                    leading: Icon(Icons.person_add),
                    title: Text('Assign Employee'),
                  ),
                ),
                const PopupMenuItem(
                  value: 'visit',
                  child: ListTile(
                    leading: Icon(Icons.home_repair_service),
                    title: Text('Create Visit'),
                  ),
                ),
                const PopupMenuItem(
                  value: 'close',
                  child: ListTile(
                    leading: Icon(Icons.check_circle),
                    title: Text('Close Complaint'),
                  ),
                ),
              ],
            ),
        ],
      ),
      body: detailState.isLoading
          ? const LoadingWidget(message: 'Loading complaint details...')
          : detailState.complaint == null
              ? _buildErrorState()
              : _buildComplaintDetails(detailState.complaint!),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Complaint not found',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildComplaintDetails(complaint) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Status',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      _buildStatusChip(complaint.status),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildInfoRow('Complaint Number', complaint.complainNumber),
                  _buildInfoRow('Service Type', complaint.serviceType),
                  _buildInfoRow('CSN', complaint.csn),
                  _buildInfoRow(
                    'Origin Date',
                    AppDateUtils.formatDate(complaint.complainOriginDate),
                  ),
                  if (complaint.registerDate != null)
                    _buildInfoRow(
                      'Register Date',
                      AppDateUtils.formatDateTime(complaint.registerDate!),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Customer Information
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Customer Information',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  if (complaint.customer != null) ...[
                    _buildInfoRow('Name', complaint.customer!.name),
                    _buildInfoRow('Mobile', complaint.customer!.mobileNo),
                    if (complaint.customer!.address != null)
                      _buildInfoRow('Address', complaint.customer!.address!),
                    if (complaint.customer!.city != null)
                      _buildInfoRow('City', complaint.customer!.city!),
                  ],
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Product Information
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Product Information',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  if (complaint.product != null) ...[
                    _buildInfoRow('Product', complaint.product!.productName),
                    _buildInfoRow('Brand', complaint.product!.brand),
                    if (complaint.product!.modelNumber != null)
                      _buildInfoRow('Model', complaint.product!.modelNumber!),
                    if (complaint.product!.serialNumber != null)
                      _buildInfoRow('Serial Number', complaint.product!.serialNumber!),
                  ],
                ],
              ),
            ),
          ),

          // Assignment Information
          if (complaint.assignedEmployee != null) ...[
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Assigned Employee',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    _buildInfoRow('Name', complaint.assignedEmployee!.name),
                    _buildInfoRow('Phone', complaint.assignedEmployee!.phoneNo),
                    if (complaint.assignedEmployee!.emailId != null)
                      _buildInfoRow('Email', complaint.assignedEmployee!.emailId!),
                  ],
                ),
              ),
            ),
          ],

          // Visits
          if (complaint.visits != null && complaint.visits!.isNotEmpty) ...[
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Visit History',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    ...complaint.visits!.map((visit) => _buildVisitItem(visit)),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color backgroundColor;
    switch (status.toUpperCase()) {
      case 'PENDING':
        backgroundColor = Colors.orange;
        break;
      case 'ASSIGNED':
        backgroundColor = Colors.blue;
        break;
      case 'RESOLVED':
        backgroundColor = Colors.green;
        break;
      case 'CANCELLED':
        backgroundColor = Colors.red;
        break;
      default:
        backgroundColor = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        status.toUpperCase(),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildVisitItem(visit) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Visit ${visit.visitId}',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              Text(
                visit.visitStatus ?? 'Unknown',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.blue,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (visit.visitDate != null)
            Text(
              'Date: ${AppDateUtils.formatDate(visit.visitDate!)}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          if (visit.technicianRemark != null)
            Text(
              'Remark: ${visit.technicianRemark}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action, complaint) {
    switch (action) {
      case 'assign':
        _showAssignDialog();
        break;
      case 'visit':
        _showCreateVisitDialog();
        break;
      case 'close':
        _showCloseDialog();
        break;
    }
  }

  void _showAssignDialog() {
    // TODO: Implement assign employee dialog
    Fluttertoast.showToast(msg: 'Assign employee feature coming soon');
  }

  void _showCreateVisitDialog() {
    // TODO: Implement create visit dialog
    Fluttertoast.showToast(msg: 'Create visit feature coming soon');
  }

  void _showCloseDialog() {
    // TODO: Implement close complaint dialog
    Fluttertoast.showToast(msg: 'Close complaint feature coming soon');
  }
}
