// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'employee.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

Employee _$EmployeeFromJson(Map<String, dynamic> json) {
  return _Employee.fromJson(json);
}

/// @nodoc
mixin _$Employee {
  int? get empId => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get phoneNo => throw _privateConstructorUsedError;
  String? get emailId => throw _privateConstructorUsedError;
  String? get address => throw _privateConstructorUsedError;
  bool? get deleted => throw _privateConstructorUsedError;
  int? get userId => throw _privateConstructorUsedError;
  int? get technicianCharge => throw _privateConstructorUsedError;

  /// Serializes this Employee to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Employee
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EmployeeCopyWith<Employee> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EmployeeCopyWith<$Res> {
  factory $EmployeeCopyWith(Employee value, $Res Function(Employee) then) =
      _$EmployeeCopyWithImpl<$Res, Employee>;
  @useResult
  $Res call({
    int? empId,
    String name,
    String phoneNo,
    String? emailId,
    String? address,
    bool? deleted,
    int? userId,
    int? technicianCharge,
  });
}

/// @nodoc
class _$EmployeeCopyWithImpl<$Res, $Val extends Employee>
    implements $EmployeeCopyWith<$Res> {
  _$EmployeeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Employee
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? empId = freezed,
    Object? name = null,
    Object? phoneNo = null,
    Object? emailId = freezed,
    Object? address = freezed,
    Object? deleted = freezed,
    Object? userId = freezed,
    Object? technicianCharge = freezed,
  }) {
    return _then(
      _value.copyWith(
            empId: freezed == empId
                ? _value.empId
                : empId // ignore: cast_nullable_to_non_nullable
                      as int?,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            phoneNo: null == phoneNo
                ? _value.phoneNo
                : phoneNo // ignore: cast_nullable_to_non_nullable
                      as String,
            emailId: freezed == emailId
                ? _value.emailId
                : emailId // ignore: cast_nullable_to_non_nullable
                      as String?,
            address: freezed == address
                ? _value.address
                : address // ignore: cast_nullable_to_non_nullable
                      as String?,
            deleted: freezed == deleted
                ? _value.deleted
                : deleted // ignore: cast_nullable_to_non_nullable
                      as bool?,
            userId: freezed == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as int?,
            technicianCharge: freezed == technicianCharge
                ? _value.technicianCharge
                : technicianCharge // ignore: cast_nullable_to_non_nullable
                      as int?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$EmployeeImplCopyWith<$Res>
    implements $EmployeeCopyWith<$Res> {
  factory _$$EmployeeImplCopyWith(
    _$EmployeeImpl value,
    $Res Function(_$EmployeeImpl) then,
  ) = __$$EmployeeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int? empId,
    String name,
    String phoneNo,
    String? emailId,
    String? address,
    bool? deleted,
    int? userId,
    int? technicianCharge,
  });
}

/// @nodoc
class __$$EmployeeImplCopyWithImpl<$Res>
    extends _$EmployeeCopyWithImpl<$Res, _$EmployeeImpl>
    implements _$$EmployeeImplCopyWith<$Res> {
  __$$EmployeeImplCopyWithImpl(
    _$EmployeeImpl _value,
    $Res Function(_$EmployeeImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Employee
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? empId = freezed,
    Object? name = null,
    Object? phoneNo = null,
    Object? emailId = freezed,
    Object? address = freezed,
    Object? deleted = freezed,
    Object? userId = freezed,
    Object? technicianCharge = freezed,
  }) {
    return _then(
      _$EmployeeImpl(
        empId: freezed == empId
            ? _value.empId
            : empId // ignore: cast_nullable_to_non_nullable
                  as int?,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        phoneNo: null == phoneNo
            ? _value.phoneNo
            : phoneNo // ignore: cast_nullable_to_non_nullable
                  as String,
        emailId: freezed == emailId
            ? _value.emailId
            : emailId // ignore: cast_nullable_to_non_nullable
                  as String?,
        address: freezed == address
            ? _value.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String?,
        deleted: freezed == deleted
            ? _value.deleted
            : deleted // ignore: cast_nullable_to_non_nullable
                  as bool?,
        userId: freezed == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int?,
        technicianCharge: freezed == technicianCharge
            ? _value.technicianCharge
            : technicianCharge // ignore: cast_nullable_to_non_nullable
                  as int?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$EmployeeImpl implements _Employee {
  const _$EmployeeImpl({
    this.empId,
    required this.name,
    required this.phoneNo,
    this.emailId,
    this.address,
    this.deleted,
    this.userId,
    this.technicianCharge,
  });

  factory _$EmployeeImpl.fromJson(Map<String, dynamic> json) =>
      _$$EmployeeImplFromJson(json);

  @override
  final int? empId;
  @override
  final String name;
  @override
  final String phoneNo;
  @override
  final String? emailId;
  @override
  final String? address;
  @override
  final bool? deleted;
  @override
  final int? userId;
  @override
  final int? technicianCharge;

  @override
  String toString() {
    return 'Employee(empId: $empId, name: $name, phoneNo: $phoneNo, emailId: $emailId, address: $address, deleted: $deleted, userId: $userId, technicianCharge: $technicianCharge)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EmployeeImpl &&
            (identical(other.empId, empId) || other.empId == empId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phoneNo, phoneNo) || other.phoneNo == phoneNo) &&
            (identical(other.emailId, emailId) || other.emailId == emailId) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.deleted, deleted) || other.deleted == deleted) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.technicianCharge, technicianCharge) ||
                other.technicianCharge == technicianCharge));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    empId,
    name,
    phoneNo,
    emailId,
    address,
    deleted,
    userId,
    technicianCharge,
  );

  /// Create a copy of Employee
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EmployeeImplCopyWith<_$EmployeeImpl> get copyWith =>
      __$$EmployeeImplCopyWithImpl<_$EmployeeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EmployeeImplToJson(this);
  }
}

abstract class _Employee implements Employee {
  const factory _Employee({
    final int? empId,
    required final String name,
    required final String phoneNo,
    final String? emailId,
    final String? address,
    final bool? deleted,
    final int? userId,
    final int? technicianCharge,
  }) = _$EmployeeImpl;

  factory _Employee.fromJson(Map<String, dynamic> json) =
      _$EmployeeImpl.fromJson;

  @override
  int? get empId;
  @override
  String get name;
  @override
  String get phoneNo;
  @override
  String? get emailId;
  @override
  String? get address;
  @override
  bool? get deleted;
  @override
  int? get userId;
  @override
  int? get technicianCharge;

  /// Create a copy of Employee
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EmployeeImplCopyWith<_$EmployeeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

EmployeeCreateRequest _$EmployeeCreateRequestFromJson(
  Map<String, dynamic> json,
) {
  return _EmployeeCreateRequest.fromJson(json);
}

/// @nodoc
mixin _$EmployeeCreateRequest {
  String get name => throw _privateConstructorUsedError;
  String get phoneNo => throw _privateConstructorUsedError;
  String? get emailId => throw _privateConstructorUsedError;
  String? get address => throw _privateConstructorUsedError;
  int? get technicianCharge => throw _privateConstructorUsedError;
  String? get username => throw _privateConstructorUsedError;
  String? get password => throw _privateConstructorUsedError;

  /// Serializes this EmployeeCreateRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EmployeeCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EmployeeCreateRequestCopyWith<EmployeeCreateRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EmployeeCreateRequestCopyWith<$Res> {
  factory $EmployeeCreateRequestCopyWith(
    EmployeeCreateRequest value,
    $Res Function(EmployeeCreateRequest) then,
  ) = _$EmployeeCreateRequestCopyWithImpl<$Res, EmployeeCreateRequest>;
  @useResult
  $Res call({
    String name,
    String phoneNo,
    String? emailId,
    String? address,
    int? technicianCharge,
    String? username,
    String? password,
  });
}

/// @nodoc
class _$EmployeeCreateRequestCopyWithImpl<
  $Res,
  $Val extends EmployeeCreateRequest
>
    implements $EmployeeCreateRequestCopyWith<$Res> {
  _$EmployeeCreateRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EmployeeCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? phoneNo = null,
    Object? emailId = freezed,
    Object? address = freezed,
    Object? technicianCharge = freezed,
    Object? username = freezed,
    Object? password = freezed,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            phoneNo: null == phoneNo
                ? _value.phoneNo
                : phoneNo // ignore: cast_nullable_to_non_nullable
                      as String,
            emailId: freezed == emailId
                ? _value.emailId
                : emailId // ignore: cast_nullable_to_non_nullable
                      as String?,
            address: freezed == address
                ? _value.address
                : address // ignore: cast_nullable_to_non_nullable
                      as String?,
            technicianCharge: freezed == technicianCharge
                ? _value.technicianCharge
                : technicianCharge // ignore: cast_nullable_to_non_nullable
                      as int?,
            username: freezed == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                      as String?,
            password: freezed == password
                ? _value.password
                : password // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$EmployeeCreateRequestImplCopyWith<$Res>
    implements $EmployeeCreateRequestCopyWith<$Res> {
  factory _$$EmployeeCreateRequestImplCopyWith(
    _$EmployeeCreateRequestImpl value,
    $Res Function(_$EmployeeCreateRequestImpl) then,
  ) = __$$EmployeeCreateRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String phoneNo,
    String? emailId,
    String? address,
    int? technicianCharge,
    String? username,
    String? password,
  });
}

/// @nodoc
class __$$EmployeeCreateRequestImplCopyWithImpl<$Res>
    extends
        _$EmployeeCreateRequestCopyWithImpl<$Res, _$EmployeeCreateRequestImpl>
    implements _$$EmployeeCreateRequestImplCopyWith<$Res> {
  __$$EmployeeCreateRequestImplCopyWithImpl(
    _$EmployeeCreateRequestImpl _value,
    $Res Function(_$EmployeeCreateRequestImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EmployeeCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? phoneNo = null,
    Object? emailId = freezed,
    Object? address = freezed,
    Object? technicianCharge = freezed,
    Object? username = freezed,
    Object? password = freezed,
  }) {
    return _then(
      _$EmployeeCreateRequestImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        phoneNo: null == phoneNo
            ? _value.phoneNo
            : phoneNo // ignore: cast_nullable_to_non_nullable
                  as String,
        emailId: freezed == emailId
            ? _value.emailId
            : emailId // ignore: cast_nullable_to_non_nullable
                  as String?,
        address: freezed == address
            ? _value.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String?,
        technicianCharge: freezed == technicianCharge
            ? _value.technicianCharge
            : technicianCharge // ignore: cast_nullable_to_non_nullable
                  as int?,
        username: freezed == username
            ? _value.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String?,
        password: freezed == password
            ? _value.password
            : password // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$EmployeeCreateRequestImpl implements _EmployeeCreateRequest {
  const _$EmployeeCreateRequestImpl({
    required this.name,
    required this.phoneNo,
    this.emailId,
    this.address,
    this.technicianCharge,
    this.username,
    this.password,
  });

  factory _$EmployeeCreateRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$EmployeeCreateRequestImplFromJson(json);

  @override
  final String name;
  @override
  final String phoneNo;
  @override
  final String? emailId;
  @override
  final String? address;
  @override
  final int? technicianCharge;
  @override
  final String? username;
  @override
  final String? password;

  @override
  String toString() {
    return 'EmployeeCreateRequest(name: $name, phoneNo: $phoneNo, emailId: $emailId, address: $address, technicianCharge: $technicianCharge, username: $username, password: $password)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EmployeeCreateRequestImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phoneNo, phoneNo) || other.phoneNo == phoneNo) &&
            (identical(other.emailId, emailId) || other.emailId == emailId) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.technicianCharge, technicianCharge) ||
                other.technicianCharge == technicianCharge) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.password, password) ||
                other.password == password));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    phoneNo,
    emailId,
    address,
    technicianCharge,
    username,
    password,
  );

  /// Create a copy of EmployeeCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EmployeeCreateRequestImplCopyWith<_$EmployeeCreateRequestImpl>
  get copyWith =>
      __$$EmployeeCreateRequestImplCopyWithImpl<_$EmployeeCreateRequestImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$EmployeeCreateRequestImplToJson(this);
  }
}

abstract class _EmployeeCreateRequest implements EmployeeCreateRequest {
  const factory _EmployeeCreateRequest({
    required final String name,
    required final String phoneNo,
    final String? emailId,
    final String? address,
    final int? technicianCharge,
    final String? username,
    final String? password,
  }) = _$EmployeeCreateRequestImpl;

  factory _EmployeeCreateRequest.fromJson(Map<String, dynamic> json) =
      _$EmployeeCreateRequestImpl.fromJson;

  @override
  String get name;
  @override
  String get phoneNo;
  @override
  String? get emailId;
  @override
  String? get address;
  @override
  int? get technicianCharge;
  @override
  String? get username;
  @override
  String? get password;

  /// Create a copy of EmployeeCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EmployeeCreateRequestImplCopyWith<_$EmployeeCreateRequestImpl>
  get copyWith => throw _privateConstructorUsedError;
}

EmployeeListItem _$EmployeeListItemFromJson(Map<String, dynamic> json) {
  return _EmployeeListItem.fromJson(json);
}

/// @nodoc
mixin _$EmployeeListItem {
  int? get empId => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get phoneNo => throw _privateConstructorUsedError;
  String? get emailId => throw _privateConstructorUsedError;
  String? get address => throw _privateConstructorUsedError;
  int? get technicianCharge => throw _privateConstructorUsedError;
  String? get username => throw _privateConstructorUsedError;
  bool? get deleted => throw _privateConstructorUsedError;
  int? get assignedComplaints => throw _privateConstructorUsedError;
  int? get resolvedComplaints => throw _privateConstructorUsedError;

  /// Serializes this EmployeeListItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EmployeeListItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EmployeeListItemCopyWith<EmployeeListItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EmployeeListItemCopyWith<$Res> {
  factory $EmployeeListItemCopyWith(
    EmployeeListItem value,
    $Res Function(EmployeeListItem) then,
  ) = _$EmployeeListItemCopyWithImpl<$Res, EmployeeListItem>;
  @useResult
  $Res call({
    int? empId,
    String name,
    String phoneNo,
    String? emailId,
    String? address,
    int? technicianCharge,
    String? username,
    bool? deleted,
    int? assignedComplaints,
    int? resolvedComplaints,
  });
}

/// @nodoc
class _$EmployeeListItemCopyWithImpl<$Res, $Val extends EmployeeListItem>
    implements $EmployeeListItemCopyWith<$Res> {
  _$EmployeeListItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EmployeeListItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? empId = freezed,
    Object? name = null,
    Object? phoneNo = null,
    Object? emailId = freezed,
    Object? address = freezed,
    Object? technicianCharge = freezed,
    Object? username = freezed,
    Object? deleted = freezed,
    Object? assignedComplaints = freezed,
    Object? resolvedComplaints = freezed,
  }) {
    return _then(
      _value.copyWith(
            empId: freezed == empId
                ? _value.empId
                : empId // ignore: cast_nullable_to_non_nullable
                      as int?,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            phoneNo: null == phoneNo
                ? _value.phoneNo
                : phoneNo // ignore: cast_nullable_to_non_nullable
                      as String,
            emailId: freezed == emailId
                ? _value.emailId
                : emailId // ignore: cast_nullable_to_non_nullable
                      as String?,
            address: freezed == address
                ? _value.address
                : address // ignore: cast_nullable_to_non_nullable
                      as String?,
            technicianCharge: freezed == technicianCharge
                ? _value.technicianCharge
                : technicianCharge // ignore: cast_nullable_to_non_nullable
                      as int?,
            username: freezed == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                      as String?,
            deleted: freezed == deleted
                ? _value.deleted
                : deleted // ignore: cast_nullable_to_non_nullable
                      as bool?,
            assignedComplaints: freezed == assignedComplaints
                ? _value.assignedComplaints
                : assignedComplaints // ignore: cast_nullable_to_non_nullable
                      as int?,
            resolvedComplaints: freezed == resolvedComplaints
                ? _value.resolvedComplaints
                : resolvedComplaints // ignore: cast_nullable_to_non_nullable
                      as int?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$EmployeeListItemImplCopyWith<$Res>
    implements $EmployeeListItemCopyWith<$Res> {
  factory _$$EmployeeListItemImplCopyWith(
    _$EmployeeListItemImpl value,
    $Res Function(_$EmployeeListItemImpl) then,
  ) = __$$EmployeeListItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int? empId,
    String name,
    String phoneNo,
    String? emailId,
    String? address,
    int? technicianCharge,
    String? username,
    bool? deleted,
    int? assignedComplaints,
    int? resolvedComplaints,
  });
}

/// @nodoc
class __$$EmployeeListItemImplCopyWithImpl<$Res>
    extends _$EmployeeListItemCopyWithImpl<$Res, _$EmployeeListItemImpl>
    implements _$$EmployeeListItemImplCopyWith<$Res> {
  __$$EmployeeListItemImplCopyWithImpl(
    _$EmployeeListItemImpl _value,
    $Res Function(_$EmployeeListItemImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EmployeeListItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? empId = freezed,
    Object? name = null,
    Object? phoneNo = null,
    Object? emailId = freezed,
    Object? address = freezed,
    Object? technicianCharge = freezed,
    Object? username = freezed,
    Object? deleted = freezed,
    Object? assignedComplaints = freezed,
    Object? resolvedComplaints = freezed,
  }) {
    return _then(
      _$EmployeeListItemImpl(
        empId: freezed == empId
            ? _value.empId
            : empId // ignore: cast_nullable_to_non_nullable
                  as int?,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        phoneNo: null == phoneNo
            ? _value.phoneNo
            : phoneNo // ignore: cast_nullable_to_non_nullable
                  as String,
        emailId: freezed == emailId
            ? _value.emailId
            : emailId // ignore: cast_nullable_to_non_nullable
                  as String?,
        address: freezed == address
            ? _value.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String?,
        technicianCharge: freezed == technicianCharge
            ? _value.technicianCharge
            : technicianCharge // ignore: cast_nullable_to_non_nullable
                  as int?,
        username: freezed == username
            ? _value.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String?,
        deleted: freezed == deleted
            ? _value.deleted
            : deleted // ignore: cast_nullable_to_non_nullable
                  as bool?,
        assignedComplaints: freezed == assignedComplaints
            ? _value.assignedComplaints
            : assignedComplaints // ignore: cast_nullable_to_non_nullable
                  as int?,
        resolvedComplaints: freezed == resolvedComplaints
            ? _value.resolvedComplaints
            : resolvedComplaints // ignore: cast_nullable_to_non_nullable
                  as int?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$EmployeeListItemImpl implements _EmployeeListItem {
  const _$EmployeeListItemImpl({
    this.empId,
    required this.name,
    required this.phoneNo,
    this.emailId,
    this.address,
    this.technicianCharge,
    this.username,
    this.deleted,
    this.assignedComplaints,
    this.resolvedComplaints,
  });

  factory _$EmployeeListItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$EmployeeListItemImplFromJson(json);

  @override
  final int? empId;
  @override
  final String name;
  @override
  final String phoneNo;
  @override
  final String? emailId;
  @override
  final String? address;
  @override
  final int? technicianCharge;
  @override
  final String? username;
  @override
  final bool? deleted;
  @override
  final int? assignedComplaints;
  @override
  final int? resolvedComplaints;

  @override
  String toString() {
    return 'EmployeeListItem(empId: $empId, name: $name, phoneNo: $phoneNo, emailId: $emailId, address: $address, technicianCharge: $technicianCharge, username: $username, deleted: $deleted, assignedComplaints: $assignedComplaints, resolvedComplaints: $resolvedComplaints)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EmployeeListItemImpl &&
            (identical(other.empId, empId) || other.empId == empId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phoneNo, phoneNo) || other.phoneNo == phoneNo) &&
            (identical(other.emailId, emailId) || other.emailId == emailId) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.technicianCharge, technicianCharge) ||
                other.technicianCharge == technicianCharge) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.deleted, deleted) || other.deleted == deleted) &&
            (identical(other.assignedComplaints, assignedComplaints) ||
                other.assignedComplaints == assignedComplaints) &&
            (identical(other.resolvedComplaints, resolvedComplaints) ||
                other.resolvedComplaints == resolvedComplaints));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    empId,
    name,
    phoneNo,
    emailId,
    address,
    technicianCharge,
    username,
    deleted,
    assignedComplaints,
    resolvedComplaints,
  );

  /// Create a copy of EmployeeListItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EmployeeListItemImplCopyWith<_$EmployeeListItemImpl> get copyWith =>
      __$$EmployeeListItemImplCopyWithImpl<_$EmployeeListItemImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$EmployeeListItemImplToJson(this);
  }
}

abstract class _EmployeeListItem implements EmployeeListItem {
  const factory _EmployeeListItem({
    final int? empId,
    required final String name,
    required final String phoneNo,
    final String? emailId,
    final String? address,
    final int? technicianCharge,
    final String? username,
    final bool? deleted,
    final int? assignedComplaints,
    final int? resolvedComplaints,
  }) = _$EmployeeListItemImpl;

  factory _EmployeeListItem.fromJson(Map<String, dynamic> json) =
      _$EmployeeListItemImpl.fromJson;

  @override
  int? get empId;
  @override
  String get name;
  @override
  String get phoneNo;
  @override
  String? get emailId;
  @override
  String? get address;
  @override
  int? get technicianCharge;
  @override
  String? get username;
  @override
  bool? get deleted;
  @override
  int? get assignedComplaints;
  @override
  int? get resolvedComplaints;

  /// Create a copy of EmployeeListItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EmployeeListItemImplCopyWith<_$EmployeeListItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

EmployeeSearchResponse _$EmployeeSearchResponseFromJson(
  Map<String, dynamic> json,
) {
  return _EmployeeSearchResponse.fromJson(json);
}

/// @nodoc
mixin _$EmployeeSearchResponse {
  int get responseCode => throw _privateConstructorUsedError;
  String get responseMessage => throw _privateConstructorUsedError;
  List<EmployeeListItem>? get response => throw _privateConstructorUsedError;
  bool? get success => throw _privateConstructorUsedError;
  int? get totalElements => throw _privateConstructorUsedError;
  int? get totalPages => throw _privateConstructorUsedError;
  int? get currentPage => throw _privateConstructorUsedError;

  /// Serializes this EmployeeSearchResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EmployeeSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EmployeeSearchResponseCopyWith<EmployeeSearchResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EmployeeSearchResponseCopyWith<$Res> {
  factory $EmployeeSearchResponseCopyWith(
    EmployeeSearchResponse value,
    $Res Function(EmployeeSearchResponse) then,
  ) = _$EmployeeSearchResponseCopyWithImpl<$Res, EmployeeSearchResponse>;
  @useResult
  $Res call({
    int responseCode,
    String responseMessage,
    List<EmployeeListItem>? response,
    bool? success,
    int? totalElements,
    int? totalPages,
    int? currentPage,
  });
}

/// @nodoc
class _$EmployeeSearchResponseCopyWithImpl<
  $Res,
  $Val extends EmployeeSearchResponse
>
    implements $EmployeeSearchResponseCopyWith<$Res> {
  _$EmployeeSearchResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EmployeeSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? responseCode = null,
    Object? responseMessage = null,
    Object? response = freezed,
    Object? success = freezed,
    Object? totalElements = freezed,
    Object? totalPages = freezed,
    Object? currentPage = freezed,
  }) {
    return _then(
      _value.copyWith(
            responseCode: null == responseCode
                ? _value.responseCode
                : responseCode // ignore: cast_nullable_to_non_nullable
                      as int,
            responseMessage: null == responseMessage
                ? _value.responseMessage
                : responseMessage // ignore: cast_nullable_to_non_nullable
                      as String,
            response: freezed == response
                ? _value.response
                : response // ignore: cast_nullable_to_non_nullable
                      as List<EmployeeListItem>?,
            success: freezed == success
                ? _value.success
                : success // ignore: cast_nullable_to_non_nullable
                      as bool?,
            totalElements: freezed == totalElements
                ? _value.totalElements
                : totalElements // ignore: cast_nullable_to_non_nullable
                      as int?,
            totalPages: freezed == totalPages
                ? _value.totalPages
                : totalPages // ignore: cast_nullable_to_non_nullable
                      as int?,
            currentPage: freezed == currentPage
                ? _value.currentPage
                : currentPage // ignore: cast_nullable_to_non_nullable
                      as int?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$EmployeeSearchResponseImplCopyWith<$Res>
    implements $EmployeeSearchResponseCopyWith<$Res> {
  factory _$$EmployeeSearchResponseImplCopyWith(
    _$EmployeeSearchResponseImpl value,
    $Res Function(_$EmployeeSearchResponseImpl) then,
  ) = __$$EmployeeSearchResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int responseCode,
    String responseMessage,
    List<EmployeeListItem>? response,
    bool? success,
    int? totalElements,
    int? totalPages,
    int? currentPage,
  });
}

/// @nodoc
class __$$EmployeeSearchResponseImplCopyWithImpl<$Res>
    extends
        _$EmployeeSearchResponseCopyWithImpl<$Res, _$EmployeeSearchResponseImpl>
    implements _$$EmployeeSearchResponseImplCopyWith<$Res> {
  __$$EmployeeSearchResponseImplCopyWithImpl(
    _$EmployeeSearchResponseImpl _value,
    $Res Function(_$EmployeeSearchResponseImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EmployeeSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? responseCode = null,
    Object? responseMessage = null,
    Object? response = freezed,
    Object? success = freezed,
    Object? totalElements = freezed,
    Object? totalPages = freezed,
    Object? currentPage = freezed,
  }) {
    return _then(
      _$EmployeeSearchResponseImpl(
        responseCode: null == responseCode
            ? _value.responseCode
            : responseCode // ignore: cast_nullable_to_non_nullable
                  as int,
        responseMessage: null == responseMessage
            ? _value.responseMessage
            : responseMessage // ignore: cast_nullable_to_non_nullable
                  as String,
        response: freezed == response
            ? _value._response
            : response // ignore: cast_nullable_to_non_nullable
                  as List<EmployeeListItem>?,
        success: freezed == success
            ? _value.success
            : success // ignore: cast_nullable_to_non_nullable
                  as bool?,
        totalElements: freezed == totalElements
            ? _value.totalElements
            : totalElements // ignore: cast_nullable_to_non_nullable
                  as int?,
        totalPages: freezed == totalPages
            ? _value.totalPages
            : totalPages // ignore: cast_nullable_to_non_nullable
                  as int?,
        currentPage: freezed == currentPage
            ? _value.currentPage
            : currentPage // ignore: cast_nullable_to_non_nullable
                  as int?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$EmployeeSearchResponseImpl implements _EmployeeSearchResponse {
  const _$EmployeeSearchResponseImpl({
    required this.responseCode,
    required this.responseMessage,
    final List<EmployeeListItem>? response,
    this.success,
    this.totalElements,
    this.totalPages,
    this.currentPage,
  }) : _response = response;

  factory _$EmployeeSearchResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$EmployeeSearchResponseImplFromJson(json);

  @override
  final int responseCode;
  @override
  final String responseMessage;
  final List<EmployeeListItem>? _response;
  @override
  List<EmployeeListItem>? get response {
    final value = _response;
    if (value == null) return null;
    if (_response is EqualUnmodifiableListView) return _response;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? success;
  @override
  final int? totalElements;
  @override
  final int? totalPages;
  @override
  final int? currentPage;

  @override
  String toString() {
    return 'EmployeeSearchResponse(responseCode: $responseCode, responseMessage: $responseMessage, response: $response, success: $success, totalElements: $totalElements, totalPages: $totalPages, currentPage: $currentPage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EmployeeSearchResponseImpl &&
            (identical(other.responseCode, responseCode) ||
                other.responseCode == responseCode) &&
            (identical(other.responseMessage, responseMessage) ||
                other.responseMessage == responseMessage) &&
            const DeepCollectionEquality().equals(other._response, _response) &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.totalElements, totalElements) ||
                other.totalElements == totalElements) &&
            (identical(other.totalPages, totalPages) ||
                other.totalPages == totalPages) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    responseCode,
    responseMessage,
    const DeepCollectionEquality().hash(_response),
    success,
    totalElements,
    totalPages,
    currentPage,
  );

  /// Create a copy of EmployeeSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EmployeeSearchResponseImplCopyWith<_$EmployeeSearchResponseImpl>
  get copyWith =>
      __$$EmployeeSearchResponseImplCopyWithImpl<_$EmployeeSearchResponseImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$EmployeeSearchResponseImplToJson(this);
  }
}

abstract class _EmployeeSearchResponse implements EmployeeSearchResponse {
  const factory _EmployeeSearchResponse({
    required final int responseCode,
    required final String responseMessage,
    final List<EmployeeListItem>? response,
    final bool? success,
    final int? totalElements,
    final int? totalPages,
    final int? currentPage,
  }) = _$EmployeeSearchResponseImpl;

  factory _EmployeeSearchResponse.fromJson(Map<String, dynamic> json) =
      _$EmployeeSearchResponseImpl.fromJson;

  @override
  int get responseCode;
  @override
  String get responseMessage;
  @override
  List<EmployeeListItem>? get response;
  @override
  bool? get success;
  @override
  int? get totalElements;
  @override
  int? get totalPages;
  @override
  int? get currentPage;

  /// Create a copy of EmployeeSearchResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EmployeeSearchResponseImplCopyWith<_$EmployeeSearchResponseImpl>
  get copyWith => throw _privateConstructorUsedError;
}
