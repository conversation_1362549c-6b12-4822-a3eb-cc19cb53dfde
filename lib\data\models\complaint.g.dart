// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'complaint.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ComplaintImpl _$$ComplaintImplFromJson(Map<String, dynamic> json) =>
    _$ComplaintImpl(
      complainId: (json['complainId'] as num?)?.toInt(),
      complainNumber: json['complainNumber'] as String,
      serviceType: json['serviceType'] as String,
      complainOriginDate: DateTime.parse(json['complainOriginDate'] as String),
      complainType: json['complainType'] as String,
      csn: json['csn'] as String,
      status: json['status'] as String,
      customer: json['customer'] == null
          ? null
          : Customer.fromJson(json['customer'] as Map<String, dynamic>),
      product: json['product'] == null
          ? null
          : Product.fromJson(json['product'] as Map<String, dynamic>),
      assignedEmployee: json['assignedEmployee'] == null
          ? null
          : Employee.fromJson(json['assignedEmployee'] as Map<String, dynamic>),
      registerDate: json['registerDate'] == null
          ? null
          : DateTime.parse(json['registerDate'] as String),
      closeDate: json['closeDate'] == null
          ? null
          : DateTime.parse(json['closeDate'] as String),
      closingRemark: json['closingRemark'] as String?,
      inOut: json['inOut'] as bool?,
      customerMisuse: json['customerMisuse'] as bool?,
      visits: (json['visits'] as List<dynamic>?)
          ?.map((e) => Visit.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ComplaintImplToJson(_$ComplaintImpl instance) =>
    <String, dynamic>{
      'complainId': instance.complainId,
      'complainNumber': instance.complainNumber,
      'serviceType': instance.serviceType,
      'complainOriginDate': instance.complainOriginDate.toIso8601String(),
      'complainType': instance.complainType,
      'csn': instance.csn,
      'status': instance.status,
      'customer': instance.customer,
      'product': instance.product,
      'assignedEmployee': instance.assignedEmployee,
      'registerDate': instance.registerDate?.toIso8601String(),
      'closeDate': instance.closeDate?.toIso8601String(),
      'closingRemark': instance.closingRemark,
      'inOut': instance.inOut,
      'customerMisuse': instance.customerMisuse,
      'visits': instance.visits,
    };

_$ComplaintListItemImpl _$$ComplaintListItemImplFromJson(
  Map<String, dynamic> json,
) => _$ComplaintListItemImpl(
  complainId: (json['complainId'] as num?)?.toInt(),
  complainNumber: json['complainNumber'] as String,
  serviceType: json['serviceType'] as String,
  complainOriginDate: DateTime.parse(json['complainOriginDate'] as String),
  complainType: json['complainType'] as String,
  csn: json['csn'] as String,
  status: json['status'] as String,
  customerName: json['customerName'] as String?,
  customerMobile: json['customerMobile'] as String?,
  productName: json['productName'] as String?,
  productBrand: json['productBrand'] as String?,
  employeeName: json['employeeName'] as String?,
  registerDate: json['registerDate'] == null
      ? null
      : DateTime.parse(json['registerDate'] as String),
  closeDate: json['closeDate'] == null
      ? null
      : DateTime.parse(json['closeDate'] as String),
  closingRemark: json['closingRemark'] as String?,
  inOut: json['inOut'] as bool?,
  customerMisuse: json['customerMisuse'] as bool?,
);

Map<String, dynamic> _$$ComplaintListItemImplToJson(
  _$ComplaintListItemImpl instance,
) => <String, dynamic>{
  'complainId': instance.complainId,
  'complainNumber': instance.complainNumber,
  'serviceType': instance.serviceType,
  'complainOriginDate': instance.complainOriginDate.toIso8601String(),
  'complainType': instance.complainType,
  'csn': instance.csn,
  'status': instance.status,
  'customerName': instance.customerName,
  'customerMobile': instance.customerMobile,
  'productName': instance.productName,
  'productBrand': instance.productBrand,
  'employeeName': instance.employeeName,
  'registerDate': instance.registerDate?.toIso8601String(),
  'closeDate': instance.closeDate?.toIso8601String(),
  'closingRemark': instance.closingRemark,
  'inOut': instance.inOut,
  'customerMisuse': instance.customerMisuse,
};

_$ComplaintCreateRequestImpl _$$ComplaintCreateRequestImplFromJson(
  Map<String, dynamic> json,
) => _$ComplaintCreateRequestImpl(
  customerCreateRequest: CustomerCreateRequest.fromJson(
    json['customerCreateRequest'] as Map<String, dynamic>,
  ),
  productCreateRequest: ProductCreateRequest.fromJson(
    json['productCreateRequest'] as Map<String, dynamic>,
  ),
  complainRegisterRequest: ComplaintRegisterRequest.fromJson(
    json['complainRegisterRequest'] as Map<String, dynamic>,
  ),
);

Map<String, dynamic> _$$ComplaintCreateRequestImplToJson(
  _$ComplaintCreateRequestImpl instance,
) => <String, dynamic>{
  'customerCreateRequest': instance.customerCreateRequest,
  'productCreateRequest': instance.productCreateRequest,
  'complainRegisterRequest': instance.complainRegisterRequest,
};

_$ComplaintRegisterRequestImpl _$$ComplaintRegisterRequestImplFromJson(
  Map<String, dynamic> json,
) => _$ComplaintRegisterRequestImpl(
  complainNumber: json['complainNumber'] as String,
  serviceType: json['serviceType'] as String,
  complainOriginDate: DateTime.parse(json['complainOriginDate'] as String),
  complainType: json['complainType'] as String,
  csn: json['csn'] as String,
  inOut: json['inOut'] as bool?,
  customerMisuse: json['customerMisuse'] as bool?,
);

Map<String, dynamic> _$$ComplaintRegisterRequestImplToJson(
  _$ComplaintRegisterRequestImpl instance,
) => <String, dynamic>{
  'complainNumber': instance.complainNumber,
  'serviceType': instance.serviceType,
  'complainOriginDate': instance.complainOriginDate.toIso8601String(),
  'complainType': instance.complainType,
  'csn': instance.csn,
  'inOut': instance.inOut,
  'customerMisuse': instance.customerMisuse,
};

_$ComplaintSearchRequestImpl _$$ComplaintSearchRequestImplFromJson(
  Map<String, dynamic> json,
) => _$ComplaintSearchRequestImpl(
  complainNumber: json['complainNumber'] as String?,
  customerName: json['customerName'] as String?,
  customerMobile: json['customerMobile'] as String?,
  status: json['status'] as String?,
  fromDate: json['fromDate'] == null
      ? null
      : DateTime.parse(json['fromDate'] as String),
  toDate: json['toDate'] == null
      ? null
      : DateTime.parse(json['toDate'] as String),
  page: (json['page'] as num?)?.toInt(),
  size: (json['size'] as num?)?.toInt(),
  sortBy: json['sortBy'] as String?,
  sortDirection: json['sortDirection'] as String?,
);

Map<String, dynamic> _$$ComplaintSearchRequestImplToJson(
  _$ComplaintSearchRequestImpl instance,
) => <String, dynamic>{
  'complainNumber': instance.complainNumber,
  'customerName': instance.customerName,
  'customerMobile': instance.customerMobile,
  'status': instance.status,
  'fromDate': instance.fromDate?.toIso8601String(),
  'toDate': instance.toDate?.toIso8601String(),
  'page': instance.page,
  'size': instance.size,
  'sortBy': instance.sortBy,
  'sortDirection': instance.sortDirection,
};

_$ComplaintSearchResponseImpl _$$ComplaintSearchResponseImplFromJson(
  Map<String, dynamic> json,
) => _$ComplaintSearchResponseImpl(
  responseCode: (json['responseCode'] as num).toInt(),
  responseMessage: json['responseMessage'] as String,
  response: (json['response'] as List<dynamic>?)
      ?.map((e) => ComplaintListItem.fromJson(e as Map<String, dynamic>))
      .toList(),
  success: json['success'] as bool?,
  totalElements: (json['totalElements'] as num?)?.toInt(),
  totalPages: (json['totalPages'] as num?)?.toInt(),
  currentPage: (json['currentPage'] as num?)?.toInt(),
);

Map<String, dynamic> _$$ComplaintSearchResponseImplToJson(
  _$ComplaintSearchResponseImpl instance,
) => <String, dynamic>{
  'responseCode': instance.responseCode,
  'responseMessage': instance.responseMessage,
  'response': instance.response,
  'success': instance.success,
  'totalElements': instance.totalElements,
  'totalPages': instance.totalPages,
  'currentPage': instance.currentPage,
};

_$VisitImpl _$$VisitImplFromJson(Map<String, dynamic> json) => _$VisitImpl(
  visitId: (json['visitId'] as num?)?.toInt(),
  complainId: (json['complainId'] as num?)?.toInt(),
  visitDate: json['visitDate'] == null
      ? null
      : DateTime.parse(json['visitDate'] as String),
  visitStatus: json['visitStatus'] as String?,
  amountCollected: (json['amountCollected'] as num?)?.toInt(),
  technicianRemark: json['technicianRemark'] as String?,
  inOut: json['inOut'] as bool?,
  customerMisuse: json['customerMisuse'] as bool?,
  technician: json['technician'] == null
      ? null
      : Employee.fromJson(json['technician'] as Map<String, dynamic>),
);

Map<String, dynamic> _$$VisitImplToJson(_$VisitImpl instance) =>
    <String, dynamic>{
      'visitId': instance.visitId,
      'complainId': instance.complainId,
      'visitDate': instance.visitDate?.toIso8601String(),
      'visitStatus': instance.visitStatus,
      'amountCollected': instance.amountCollected,
      'technicianRemark': instance.technicianRemark,
      'inOut': instance.inOut,
      'customerMisuse': instance.customerMisuse,
      'technician': instance.technician,
    };

_$ComplaintTransferRequestImpl _$$ComplaintTransferRequestImplFromJson(
  Map<String, dynamic> json,
) => _$ComplaintTransferRequestImpl(
  visitStatus: json['visitStatus'] as String,
  amountCollected: (json['amountCollected'] as num).toInt(),
  inOut: json['inOut'] as bool?,
  customerMisuse: json['customerMisuse'] as bool?,
  technicianRemark: json['technicianRemark'] as String,
  visitDate: DateTime.parse(json['visitDate'] as String),
);

Map<String, dynamic> _$$ComplaintTransferRequestImplToJson(
  _$ComplaintTransferRequestImpl instance,
) => <String, dynamic>{
  'visitStatus': instance.visitStatus,
  'amountCollected': instance.amountCollected,
  'inOut': instance.inOut,
  'customerMisuse': instance.customerMisuse,
  'technicianRemark': instance.technicianRemark,
  'visitDate': instance.visitDate.toIso8601String(),
};

_$CloseComplaintRequestImpl _$$CloseComplaintRequestImplFromJson(
  Map<String, dynamic> json,
) => _$CloseComplaintRequestImpl(
  closingRemark: json['closingRemark'] as String,
  closeDate: DateTime.parse(json['closeDate'] as String),
);

Map<String, dynamic> _$$CloseComplaintRequestImplToJson(
  _$CloseComplaintRequestImpl instance,
) => <String, dynamic>{
  'closingRemark': instance.closingRemark,
  'closeDate': instance.closeDate.toIso8601String(),
};
