import 'package:freezed_annotation/freezed_annotation.dart';

part 'employee.freezed.dart';
part 'employee.g.dart';

@freezed
class Employee with _$Employee {
  const factory Employee({
    int? empId,
    required String name,
    required String phoneNo,
    String? emailId,
    String? address,
    bool? deleted,
    int? userId,
    int? technicianCharge,
  }) = _Employee;

  factory Employee.fromJson(Map<String, dynamic> json) => _$EmployeeFromJson(json);
}

@freezed
class EmployeeCreateRequest with _$EmployeeCreateRequest {
  const factory EmployeeCreateRequest({
    required String name,
    required String phoneNo,
    String? emailId,
    String? address,
    int? technicianCharge,
    String? username,
    String? password,
  }) = _EmployeeCreateRequest;

  factory EmployeeCreateRequest.fromJson(Map<String, dynamic> json) => 
      _$EmployeeCreateRequestFromJson(json);
}

@freezed
class EmployeeListItem with _$EmployeeListItem {
  const factory EmployeeListItem({
    int? empId,
    required String name,
    required String phoneNo,
    String? emailId,
    String? address,
    int? technicianCharge,
    String? username,
    bool? deleted,
    int? assignedComplaints,
    int? resolvedComplaints,
  }) = _EmployeeListItem;

  factory EmployeeListItem.fromJson(Map<String, dynamic> json) => 
      _$EmployeeListItemFromJson(json);
}

@freezed
class EmployeeSearchResponse with _$EmployeeSearchResponse {
  const factory EmployeeSearchResponse({
    required int responseCode,
    required String responseMessage,
    List<EmployeeListItem>? response,
    bool? success,
    int? totalElements,
    int? totalPages,
    int? currentPage,
  }) = _EmployeeSearchResponse;

  factory EmployeeSearchResponse.fromJson(Map<String, dynamic> json) => 
      _$EmployeeSearchResponseFromJson(json);
}
